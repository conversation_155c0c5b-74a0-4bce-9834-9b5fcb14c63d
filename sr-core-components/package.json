{"version": "0.1.210", "description": "SmartReach's shared components", "author": "Smartreach", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "engines": {"node": ">=10"}, "scripts": {"start": "tsdx watch", "build": "tsdx build", "test": "tsdx test", "lint": "tsdx lint", "prepare": "tsdx build", "size": "size-limit", "analyze": "size-limit --why", "artifactregistry-login": "npx google-artifactregistry-auth"}, "peerDependencies": {}, "husky": {"hooks": {"pre-commit": "tsdx lint"}}, "prettier": {"printWidth": 80, "semi": true, "singleQuote": true, "trailingComma": "es5"}, "name": "@sr/shared-product-components", "module": "dist/shared-product-components.esm.js", "devDependencies": {"@size-limit/preset-small-lib": "^11.1.2", "@sr/design-component": "1.1.60", "@tinymce/tinymce-react": "3.13.1", "@types/lodash": "^4.14.116", "@types/react": "17.0.2", "@types/react-datepicker": "4.4.2", "husky": "^9.0.11", "libphonenumber-js": "^1.10.39", "lodash": "^4.17.11", "process": "^0.11.10", "tsdx": "^0.14.1", "tslib": "^2.6.2", "typescript": "^3.9.10"}, "dependencies": {"formik": "^2.2.9", "moment-timezone": "^0.5.38", "react": "17.0.2", "react-datepicker": "^4.8.0", "react-dom": "17.0.2"}}