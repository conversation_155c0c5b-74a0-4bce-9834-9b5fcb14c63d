// // Place your settings in this file to overwrite default and user settings.
// {
//   "typescript.tsdk": "post-login/node_modules/typescript/lib",
//   "files.trimTrailingWhitespace": true,
//   "editor.insertSpaces": true,
//   "editor.tabSize": 2,
//   "editor.formatOnSave": true,
//   "editor.detectIndentation": false,
//   "html.format.indentInnerHtml": true,
//   "html.format.wrapLineLength": 120,
//   "css.validate": true,
//   "html.suggest.html5": true,
//   "eslint.autoFixOnSave": true,
//   "eslint.enable": true,
//   "eslint.run": "onType",
//   "tslint.autoFixOnSave": true,
//   "tslint.enable": true,
//   "tslint.configFile": "tslint.json",
//   "raml.previewTheme": "light",
//   "files.autoSave": "onFocusChange",
// }
