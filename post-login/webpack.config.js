"use strict";

const path = require("path");
const loaders = require("./webpack/loaders");
const plugins = require("./webpack/plugins");
const TerserPlugin = require("terser-webpack-plugin");

const applicationEntries = process.env.NODE_ENV === "development" ? [] : [];

const config = {
  // REF: https://webpack.js.org/configuration/cache/
  cache: true,

  mode: process.env.NODE_ENV === "production" ? "production" : "development",

  entry: ["./client/index.tsx"].concat(applicationEntries),

  output: {
    // NOTE: https://gitlab.com/heaplabs/coldemail/issues/21
    path: path.join(__dirname, "sr/public/public/assets"),
    filename:
      process.env.NODE_ENV === "production"
        ? "[name].[contenthash].js"
        : "[name].js",
    publicPath: "/",
    sourceMapFilename:
      process.env.NODE_ENV === "production"
        ? "[name].[contenthash].js.map"
        : "[file].map[query]",
    // chunkFilename: '[id].chunk.js',
    // chunkFilename: '[id].chunk.[contenthash].js',
    chunkFilename:
      process.env.NODE_ENV === "production"
        ? "[name].chunk.[chunkhash].js"
        : "[name].chunk.js",
    crossOriginLoading: "anonymous", // the following setting is required for SRI to work:
  },

  devtool:
    process.env.NODE_ENV === "development"
      ? "cheap-module-eval-source-map"
      : "nosources-source-map",

  resolve: {
    extensions: [
      // '',
      ".webpack.js",
      ".web.js",
      ".tsx",
      ".ts",
      ".js",
      ".json",
    ],

    fallback: {
      // REF: https://stackoverflow.com/a/64580815
      util: require.resolve("util/"),

      // REF: https://stackoverflow.com/a/65018686
      process: "process/browser",
    },

    alias: {
      "react/jsx-runtime": "react/jsx-runtime.js",
    },
  },

  stats: {
    errorDetails: true,
  },

  plugins: plugins,

  devServer: {
    allowedHosts: "all",

    historyApiFallback: { index: "/" },
    // proxy: Object.assign({}, {
    //   '/api/*': {
    //     target: 'https://devapi.sreml.com',
    //     secure: false,
    //     changeOrigin: true
    //   }
    // }),

    proxy: Object.assign({}, { "/api/*": "http://localhost:9000/" }),
    port: 3001,
  },

  module: {
    // preLoaders: [
    //   loaders.tslint,
    //   // All output '.js' files will have any sourcemaps re-processed
    //   // by 'source-map-loader'.
    //   { test: /\.js$/, loader: 'source-map-loader' },
    // ],
    rules: [
      // loaders.tslint,
      //9-Feb-2024: babel-loader is added for cypress code coverage
      //ref: https://github.com/ubbe-xyz/cypress-typescript-coverage-example/blob/master/webpack.config.js

      { test: /\.js$/, loader: "source-map-loader", enforce: "pre" },
      loaders.tsx,
      loaders.html,
      loaders.scss,
      loaders.css,
      loaders.fontLoader,
    ],
  },
  externals: {
    "react/lib/ReactContext": "window",
    "react/lib/ExecutionEnvironment": true,
    "react/addons": true,
  },
};

if (process.env.NODE_ENV === "production") {
  config["optimization"] = {
    usedExports: true,

    chunkIds: "named",
    runtimeChunk: "single",

    // before 14-may-2024
    //splitChunks: {
    //  chunks: 'all',

    //  // Update: commented-out maxSize cause it was breaking the build in the browser
    //  // http2 prefers multiple smaller chunks compared to large fewer chunks
    //  // maxSize: 512000,

    //  // cacheGroups: {
    //  //   commons: {
    //  //     test: /[\\/]node_modules[\\/]/,
    //  //     name: "vendor",
    //  //     chunks: "all",
    //  //   }
    //  // }
    //},

    splitChunks: {
      chunks: "all",
      minSize: 0,
      maxSize: Infinity,
      cacheGroups: {
        node_modules: {
          test: /[\\/]node_modules[\\/]/,
          name(module) {
            const packageName = module.context.match(
              /[\\/]node_modules[\\/](.*?)([\\/]|$)/
            )[1];
            return packageName;
          },
        },
      },
    },

    minimize: true,
    minimizer: [
      new TerserPlugin({
        parallel: true,
        terserOptions: {
          ie8: false,
          safari10: false,

          //REF: taken from https://stackoverflow.com/a/57362733
          parse: {
            // we want terser to parse ecma 8 code. However, we don't want it
            // to apply any minfication steps that turns valid ecma 5 code
            // into invalid ecma 5 code. This is why the 'compress' and 'output'
            // sections only apply transformations that are ecma 5 safe
            // https://github.com/facebook/create-react-app/pull/4234
            ecma: 8,
          },
          compress: {
            ecma: 5,
            warnings: false,
            // Disabled because of an issue with Uglify breaking seemingly valid code:
            // https://github.com/facebook/create-react-app/issues/2376
            // Pending further investigation:
            // https://github.com/mishoo/UglifyJS2/issues/2011
            comparisons: false,
            // Disabled because of an issue with Terser breaking valid code:
            // https://github.com/facebook/create-react-app/issues/5250
            // Pending futher investigation:
            // https://github.com/terser-js/terser/issues/120
            inline: 2,
          },
          mangle: {
            safari10: true,
          },
          output: {
            ecma: 5,
            comments: false,
            // Turned on because emoji and regex is not minified properly using default
            // https://github.com/facebook/create-react-app/issues/2488
            ascii_only: true,
          },
        },
      }),
    ],
  };
}

module.exports = config;
