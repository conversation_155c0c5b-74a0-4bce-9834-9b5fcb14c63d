import { SrServer as server } from './server';
import { crispTrackEvent } from '../utils/crisp';
const url = '/api/v2/campaigns';
const urlV3 = '/api/v3/campaigns';
import * as queryString from 'query-string';
import { IVariantAndStepID } from '../containers/campaign/new-multichannel-flow/preview-tab-mc';
import { SRSearch, Campaigns } from '@sr/shared-product-components';
import { CustomNode, CustomNodeData, NodeType } from '../containers/campaign/drip-campaign/models';
import { Edge } from '@xyflow/react';
import { IVoiceDropData } from '../containers/voicemail-settings';
import { VoiceCall } from '../containers/tasks/voice-call';


// interface ICampaignList {
//   campaigns: Campaigns.ICampaign[];
//   has_archived_campaign: boolean;
// }

interface INewCampaign {
  campaign: Campaigns.ICampaign;
}

interface ICampaignSteps {
  campaign_steps: Campaigns.ICampaignStep[];
}

export interface ICampaignBasicInfo {
  campaign: Campaigns.ICampaign;
}

export interface ChannelSetupData {
  channel_type: Campaigns.ChannelType;
  team_id: number;
  campaign_id: number;
  channel_setting_uuid: string;
}
interface IChannelSettings {
  channel_setup_data: ChannelSetupData[];
}

interface IGeneralSetting {
  id: string
}

interface IEmailSetting {
  id: string
}

interface ISmsSetting {
  id: string
}

interface ICallSetting {
  id: string
}

interface ILinkedinSetting {
  id: string
}

interface IWhatsappSetting {
  id: string
}

interface IChannelSettingsV3Data {
  general_settings: IGeneralSetting[];
  email_settings: IEmailSetting[];
  sms_settings: ISmsSetting[];
  call_settings: ICallSetting[];
  linkedin_settings: ILinkedinSetting[];
  whatsapp_settings: IWhatsappSetting[];
}

interface IChannelSettingsV3 {
  channel_settings: IChannelSettingsV3Data;
}

export interface IBasicCampaignList {
  id: number,
  name: string,
  owner_name: string,
  has_edit_campaign_permission: boolean,
  created_at: number,
  uuid: string
}

export interface ReorderCampaignStepData {
  step_id_to_be_reordered: number;
  new_parent_step_id: number;
}

interface IBasicCampaignListResponse {
  campaigns: IBasicCampaignList[];
}

export interface IDripCampaignForm {

  nodes: CustomNode<CustomNodeData, NodeType>[],
  edges: Edge[],
  headNodeId: string

}

export interface ICampaignTypeData {

  nodes: CustomNode<CustomNodeData, NodeType>[]
  edges: Edge[]
  head_node_id: String

}


export function assignProspectsToCampaign(campaignId: number | string, prospect_ids: number[], ignore_prospects_active_in_other_campaigns: boolean) {
  return server.post(url + '/' + campaignId + '/prospects', { prospect_ids: prospect_ids, ignore_prospects_active_in_other_campaigns: ignore_prospects_active_in_other_campaigns });
}


export function delProspectsfromCampaign(campaignId: string | number, prospectIds: number[]) {
  return server.del(url + '/' + campaignId + '/prospects', { prospect_ids: prospectIds });
}

export function assignProspectsToCampaignBatch(campaignId: number | string, prospect_ids: string[], ignore_prospects_active_in_other_campaigns: boolean, isSelectAll?: boolean, filterObj?: SRSearch.ISearchAPIRequest) {
  const inputData = {
    ignore_prospects_active_in_other_campaigns: ignore_prospects_active_in_other_campaigns,
    prospect_ids: (!isSelectAll ? prospect_ids : undefined),
    campaign_id: campaignId,
    is_select_all: isSelectAll,
    filters: (!!isSelectAll ? filterObj : undefined),
  }
  return server.post('/api/v2/prospects/assign_to_campaign', inputData);
}


export function unassignProspectsFromCampaignBatch(campaignId: string | number, prospectIds: string[], isSelectAll?: boolean, filterObj?: SRSearch.ISearchAPIRequest) {
  const inputData = {
    prospect_ids: (!isSelectAll ? prospectIds : undefined),
    campaign_id: campaignId,
    is_select_all: isSelectAll,
    filters: (!!isSelectAll ? filterObj : undefined),
  }
  return server.post('/api/v2/prospects/unassign_from_campaign', inputData);
}


// export function getCampaignSettings(campaignId: string | number) {
//   return server.get<ISettings>(url + '/' + campaignId, { hideSuccess: true });
// }

/* 1. Get all campaigns list*/
// export function getCampaignList(archived?: boolean) {
//   if (archived) {
//     return server.get<ICampaignList>('/api/v2/campaigns?archived=true', { hideSuccess: true });
//   } else {
//     return server.get<ICampaignList>('/api/v2/campaigns', { hideSuccess: true });
//   }
// }

export function getCampaignById(campaignId: number | string) {
  return server.get<{ campaign: Campaigns.ICampaign }>('/api/v2/campaigns/' + campaignId, { hideSuccess: true });
}
export function getCampaignStatsById(cid: string, tid?: number) {
  return server.get<Campaigns.ICampaignStats>('/api/v2/campaigns/' + cid + '/stats', { hideSuccess: true });
}

/* 2. Create a new campaign*/
export function createNewCampaignId(zone: string, owner_id: number, campaign_type: Campaigns.campaign_type) {
  const data = { timezone: zone, campaign_owner_id: owner_id, campaign_type: campaign_type };
  return server.post<INewCampaign>('/api/v2/campaigns', data, { hideSuccess: true })
    .then((res) => {
      crispTrackEvent('Create_New_Campaign');
      return res;
    });
}

/* 3. Get campaign steps */
export function getCampaignSteps(campaignId: number | string) {
  return server.get<ICampaignSteps>('/api/v2/campaigns/' + campaignId + '/steps', { hideSuccess: true });
}

/* 4. Save campaign steps */
// export function saveCampaignSteps(emails: Campaigns.ICampaignStep[], campaignId: number | string) {
//   const parentId = 0;
//   _.map(emails, (email) => {
//     const data = {
//       parent_id: parentId,
//       body: email.body,
//       subject: email.subject,
//       delay: email.delay,
//     };
//     return server.post(url + '/' + campaignId + '/steps', data);
//   });
// }

export interface SaveStepResponse {
  step_id: number;
  variant_id: number;
}

export function saveCampaignStep(data: Campaigns.ISaveStepVariant) {

  //CASE 1 variantId and stepId exists edit variant
  //CASE 2 If step_id is not null in data backend creates new varints
  //CASE 3 if step_id is null in data from front end backend creates new step

  if (data.stepId && data.variantId) { //UPDATE VARIANT
    console.log("UPDATE VARIANT")
    return server.put<SaveStepResponse>('/api/v2/campaigns/' + data.campaignId + '/steps/' + data.stepId + '/variants/' + data.variantId, data.stepVariant, { hideSuccess: true });
  } else { //CREATE VARIANT
    console.log("CREATE VARIANT")
    return server.post<SaveStepResponse>('/api/v2/campaigns/' + data.campaignId + '/steps/' + data.stepId + '/variants', data.stepVariant, { hideSuccess: true });
  }
  // else { //CREATE STEP
  //   console.log("CREATE STEP")
  //   return server.post<ICampaignStep>(url + '/' + campaignId + '/steps', data);
  // }

}

export function updateVariantActiveStatus(data: {
  active: boolean,
  variantId: number,
  campaignId: number,
  stepId: number
}) {
  return server.put(
    `/api/v2/campaigns/${data.campaignId}/steps/${data.stepId}/variants/${data.variantId}/status`,
    { active: data.active }
  );
}

export function reorderCampaignSteps({
  campaignId,
  reorderCampaignStepData,
}: {
  campaignId: number;
  reorderCampaignStepData: ReorderCampaignStepData;
}) {
  return server.put<Campaigns.ICampaignStep[]>(
    `/api/v2/campaigns/${campaignId}/steps/reorder`,
    reorderCampaignStepData
  );
}

/* 5. Update Campaign Name */
export function updateCampaignName(newCampaignName: string, campaignId: number | string) {
  const data = { name: newCampaignName };
  return server.put('/api/v2/campaigns/' + campaignId, data, { hideSuccess: true });
}

/* 6.start campaign */
export function startCampaign(campaignId: number | string, schedule_start_at?: number, schedule_start_at_tz?: string) {
  console.log('start campaign argummennts', arguments);
  if (!!schedule_start_at) {
    const data = {
      status: 'scheduled',
      schedule_start_at: schedule_start_at,
      time_zone: schedule_start_at_tz,
    };
    return server.put<ICampaignBasicInfo>('/api/v2/campaigns/' + campaignId + '/status', data, { hideSuccess: true })
      .then((res) => {
        crispTrackEvent('Start_Campaign');
        return res;
      });
  } else {
    const data = {
      status: 'running'
    };
    return server.put<ICampaignBasicInfo>('/api/v2/campaigns/' + campaignId + '/status', data, { hideSuccess: true })
      .then((res) => {
        crispTrackEvent('Start_Campaign');
        return res;
      });
  }
}

/* stop campaign */
export function stopCampaign(campaignId: number | string) {
  const data = {
    status: 'stopped'
  };
  return server.put<ICampaignBasicInfo>('/api/v2/campaigns/' + campaignId + '/status', data)
    .then((res) => {
      crispTrackEvent('Pause_Campaign');
      return res;
    });
}

export function updateCampaignScheduleSettings(campaignId: number | string, data: any) {
  return server.put('/api/v2/campaigns/' + campaignId + '/settings', data);
}


export function uploadVoicemails(campaignId: number | string,
  data: FormData, tid: number) {
  const formDataObj: Record<string, any> = {};
  data.forEach((value, key) => {
    formDataObj[key] = value;
  });
  return server.putFormData('/api/v2/campaigns/' + campaignId + '/upload_voicemail', data, { hideSuccess: true })
}


export function getVoicemails(campaignId: number | string) {
  return server.get<{ voicemails: IVoiceDropData[] }>('/api/v2/campaigns/' + campaignId + '/voicemails', { hideSuccess: true })
}

export function deleteVoicemail(campaignId: number, voicemailId: number) {
  const data = {};

  return server.del('/api/v2/campaigns/' + campaignId + '/voicemail/' + voicemailId, data, { hideSuccess: true })
}

export function updateVoicemail(campaignId: number, voicemailId: number, data: VoiceCall.IVoicemailUpdate) {


  return server.put('/api/v2/campaigns/' + campaignId + '/voicemail/' + voicemailId, data, { hideSuccess: true })
}



export function unsubscribe(code: string) {
  return server.get(`${url}/unsubscribe?code=${code}`, { hideSuccess: true });
}

export function unsubscribeV2(code: string) {
  return server.get(`${url}/unsubscribe_v2?code=${code}`, { hideSuccess: true });
}

export function sendTestMail(campaignId: number | string, data: Campaigns.ITestMail) {
  return server.post(url + '/' + campaignId + '/steps/send_test', data);
}

export function deleteCampaignStepVariant(campaignId: number | string, stepId: number | string, variantId: number | string) {
  const data = {};
  return server.del(url + '/' + campaignId + '/steps/' + stepId + '/variants/' + variantId, data);
}

export function updateOptOutSettings(campaignId: number | string, optOutIsText: boolean, optOutMsg: string, addProspectToDncOnOptOut: boolean) {
  const data = {
    opt_out_is_text: optOutIsText,
    opt_out_msg: optOutMsg,
    add_prospect_to_dnc_on_opt_out: addProspectToDncOnOptOut
  };
  return server.put<ICampaignBasicInfo>(url + '/' + campaignId + '/opt_out_settings', data);
}

export function getProspectsForPreviewV2(
  campaignId: number,
  pageNum: number,
  cesid?: number,
  search?: string
) {

  const defaultPageNum = pageNum || 1;

  const searchParam = queryString.stringify({
    q: search,
    cesid: cesid,
    page: defaultPageNum
  }, { skipNull: true });
  return server.get<{ prospects: Campaigns.IProspectForPreview[] }>(url + '/' + campaignId + '/previews/prospects?' + searchParam, { hideSuccess: true });

}

export function getPreviewsForProspect(
  campaignId: number,
  prospectId: number,
  stepsAndVariant: IVariantAndStepID[],
  selected_campaign_email_setting_id?: number
) {
  const searchParam = selected_campaign_email_setting_id ? `?cesid=${selected_campaign_email_setting_id}` : '';
  return server.post<Campaigns.IProspectPreview>(
    `${url}/${campaignId}/previews/prospects/${prospectId}` + searchParam,
    stepsAndVariant,
    { hideSuccess: true }
  );

}

export function updatePreviewForProspect(data: {
  campaignId: number,
  prospectId: number,
  stepId: number,
  editedSubject: string,
  editedBody: string,
}) {
  return server.post(
    `${url}/${data.campaignId}/previews/prospects/${data.prospectId}/steps/${data.stepId}`,

    {
      edited_subject: data.editedSubject,
      edited_body: data.editedBody
    },

    { hideSuccess: true }
  );
}




export function updateAdditionalSettings(campaignId: number | string, data: any) {
  return server.put<ICampaignBasicInfo>(url + '/' + campaignId + '/other_settings', data);
}

export function createDuplicateCampaign(campaignId: number | string) {
  return server.post<{ campaign: Campaigns.ICampaign }>(url + '/' + campaignId + '/duplicate', {});
}

export function startWarmup(campaignId: number | string, warmup_length_in_days: number, warmup_starting_email_count: number) {
  const data = { warmup_length_in_days: warmup_length_in_days, warmup_starting_email_count: warmup_starting_email_count };
  return server.put<ICampaignBasicInfo>(url + '/' + campaignId + '/start_warmup', data, { hideSuccess: true })
    .then((res) => {
      crispTrackEvent('Update_Warmup_ON');
      return res;
    });
}

export function stopWarmup(campaignId: number | string, hideSuccess?: boolean) {
  return server.put<ICampaignBasicInfo>(url + '/' + campaignId + '/stop_warmup', {}, { hideSuccess: !!hideSuccess });
}

export function deleteCampaign(campaignId: number | string) {
  crispTrackEvent('Delete_campaign');
  return server.del(url + '/' + campaignId, {});
}

export function getBasicCampaignList(is_campaign_inbox?: boolean) {
  if (!!is_campaign_inbox) {

    return server.get<IBasicCampaignListResponse>(url + '?basic=true' + `&is_campaign_inbox=${is_campaign_inbox}`, { hideSuccess: true });

  } else {

    return server.get<IBasicCampaignListResponse>(url + '?basic=true', { hideSuccess: true });

  }

}

export function updateCampaignEmailSettingsV2(campaignId: number | string, data: {
  sender_email_settings_id: number;
  receiver_email_settings_id: number;
}[]) {
  return server.put(url + '/' + campaignId + '/email_settings_v2', data);
}

export function updateCampaignMaxEmailPerDay(campaignId: number | string, data: number) {
  return server.put(url + '/' + campaignId + '/max_emails_per_day', data, { hideSuccess: true });
}



export function downloadCampaignReportV3(campaignIds: string[] | number[], from: number | string, till: number | string) {
  const data = {
    campaign_ids: campaignIds,
    from: from,
    till: till,
  };
  const query = queryString.stringify(data);
  console.log('download report query', query);
  return server.fetch(url + '/download_report?' + query);
}


export function updateAppendFollowUps(campaignId: string | number, data: { append_followups: Boolean }) {
  return server.put(url + '/' + campaignId + '/append_followups', data);
}

export function updateArchiveStatus(campaignId: number | string, data: { archive: boolean }) {
  return server.put(url + '/' + campaignId + '/archive', data);
}

export function unlinkTemplate(campaignId: number | string, stepId: number | string, variantId: number | string) {
  return server.put<{}>(url + '/' + campaignId + '/steps/' + stepId + '/variants/' + variantId + '/unlink_template', {});
}


export function getOrgEmailSendingStatus(campaignId: number) {
  return server.get<SpamReview.IEmailSendingStatusForAllTypes>(url + '/' + campaignId + '/email_sending_status', { hideSuccess: true });
}

export function updateChannelSettings(campaignId: number, data: ChannelSetupData) {
  return server.put<IChannelSettings>(url + '/' + campaignId + '/campaign_channel_setup',
    data, { hideSuccess: true });
}
export function getChannelSettings(campaignUuid: string) {
  return server.getV3<IChannelSettingsV3>(urlV3 + '/' + campaignUuid + '/channel_settings', { hideSuccess: true });
}

export function saveDripCampaign(data: IDripCampaignForm, campaignId: number) {

  return server.put(url + '/' + campaignId + '/save_drip', data, { hideSuccess: true })

}

export function getCampaignData(campaignId: number) {
  return server.get<ICampaignTypeData>(url + '/' + campaignId + '/get_campaign_data', { hideSuccess: true, hideError: true })
}