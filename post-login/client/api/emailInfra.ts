import { DomainSearchResult, ZapmailDomainSearchResult } from '../containers/account/maildoso/buy_new_domains';
import { EmailInfraWorkspaceType, PurchasedDomainAndEmailForFrontend } from '../containers/account/maildoso/register_confirmation';
import { SrServer as server } from './server';

export type emailInfraPlatformType = 'maildoso' | 'zapmail'

export type ZapmailWorkspaceType = 'GOOGLE' | 'MICROSOFT'

export interface IRequiredEmailAndDomains {
  required_purchased_emails_qty: number
  required_purchased_domains_qty: number
}

export async function searchZapmailDomains(domains: EmailInfra.IZapMailDomainRequest) {
  const { data } = await server.post<ZapmailDomainSearchResult[]>(
    '/api/v2/email-infra/zapMail/search-domains',
    { domainName: domains.domainName, tlds: domains.tlds, years: domains.years }
  );
  return data;
}

export async function searchDomains(domains: string[]) {
  const { data } = await server.post<DomainSearchResult>(
    '/api/v2/email-infra/search-domains',
    { domains }
  );
  return data;
}


export async function zapMailSaveDomains(
  domainInfo: {
    domains: { [key: string]: EmailInfra.ZapMailMailboxInfo[] },
    domainRedirectTo: string,
    workspaceType: ZapmailWorkspaceType,
    has_acknowledged?: boolean
  }, tid: number) {
  const { data } = await server.post<string>(
    `/api/v2/email-infra/zapmail/purchase?tid=${tid}`,
    domainInfo
  );
  return data;
}

export async function zapMailSaveAdditionalDomains(
  domainInfo: {
    domain: string,
    emails: EmailInfra.ZapMailMailboxInfo[],
    workspaceType: ZapmailWorkspaceType,
    has_acknowledged?: boolean
  }, tid: number
) {
  const { data } = await server.post<string>(
    `/api/v2/email-infra/zapmail/additional_purchase?tid=${tid}`,
    domainInfo
  );
  return data;
}

export async function getRequiredAddonsCountForDomainsAndEmailsPurchase(
  purchaseInfo: {
    emails_to_purchase: string[],
    domains_to_purchase: string[],
    platform_type: emailInfraPlatformType,
    workspace_type: EmailInfraWorkspaceType
  }, tid: number) {

  const { data } = await server.post<IRequiredEmailAndDomains>(
    `/api/v2/email-infra/get-addons-count?tid=${tid}`,
    purchaseInfo
  )

  return data
}

export async function saveDomains(data: any, tid: number) {
  const { data: response } = await server.post<{ data: any }>(
    `/api/v2/email-infra/save-domains?tid=${tid}`,
    data
  );
  return response;
}

export async function saveAdditionalEmail(domain_uuid: any, emails: EmailInfra.IPurchaseEmail[]) {
  const { data: response } = await server.post<{ data: any }>(
    `/api/v2/email-infra/emails/${domain_uuid}`,
    { purchasedEmails: emails }
  );
  return response;
}

export async function fetchAllDomainsAndEmails() {
  const { data } = await server.get<PurchasedDomainAndEmailForFrontend[]>(
    '/api/v2/email-infra/get_purchased_domains_and_emails',
    { hideSuccess: true }
  );
  return data;
}

export async function deleteDomains(purchasedDomainUuids: string[], platformType: emailInfraPlatformType) {
  const { data } = await server.del<{ success: boolean }>(
    '/api/v2/email-infra/domains',
    { "purchasedDomainUuids": purchasedDomainUuids, "platformType": platformType }
  );
  return data;
}

export async function deleteEmail(emailSettingId: number) {

  const { data } = await server.del<{ success: boolean }>(
    '/api/v2/email-infra/emails',
    { "emailSettingId": emailSettingId }
  );
  return data;
}

export async function getAddonsCountForDomainsAndEmailsPurchase(emails_to_purchase: number, domains_to_purchase: number, platform_type: emailInfraPlatformType) {

  const { data } = await server.get<IRequiredEmailAndDomains>(
    `/api/v2/email-infra/get-addons-count?emails_to_purchase=${emails_to_purchase}&domains_to_purchase=${domains_to_purchase}&platform_type=${platform_type}`,
    {
      hideSuccess: true
    }
  )

  return data
}

export async function updateEmail(data: {
  email: string,
  firstName: string,
  lastName: string,
  profilePicture?: string
}) {
  const { data: response } = await server.put<{ success: boolean }>(
    `/api/v2/email-infra/zapmail/update-mailbox`,
    {
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      profilePicture: data.profilePicture
    }
  );
  return response;
}

export function reconnectEmailAccount(emailAddress: string) {
  return server.post<{ success: boolean }>(
    `/api/v2/email-infra/zapmail/oauth/reconnect`,
    {
      emailAddress: emailAddress,
    }
  );
}

export interface RecentPurchaseInfo {
  team_id: number;
  account_id: number;
  domain_name: string;
  purchased_emails: string[];
  created_at: string;
  provider: string;
  purchase_type: string;
}

export async function getRecentPurchases() {
  const { data } = await server.get<RecentPurchaseInfo[]>(
    '/api/v2/email-infra/recent-purchases',
    { hideSuccess: true }
  );
  return data;
}
