import { inject, observer } from 'mobx-react';
import React from 'react';
import { RouteComponentProps, with<PERSON>out<PERSON> } from 'react-router';
import { LogIn, Prospects, Task } from '@sr/shared-product-components'
import { SRLink } from '../../components/helpers';
import * as prospectsApi from '../../api/prospects';
import * as settingsApi from '../../api/settings';
import _ from 'lodash';
import { classNames } from '../../utils/sr-utils';
import { ISRDropdownOption, SRButtonFilled, SRPopover, SRSimpleSelectionDropdown } from '@sr/design-component';
import { getOptionsForSelectPhone } from './tasks-utils';

type WhatsAppTaskData = Task.ISendWhatsAppMessageTaskData;

function isWhatsAppTaskData(data: Task.ITaskData): data is WhatsAppTaskData {
  return data.task_type === 'send_whatsapp_message';
}

// Helper to safely get WhatsApp body
export function getWhatsAppBody(data: Task.ITaskData): string | undefined {
    if (isWhatsAppTaskData(data) && 'body' in data) {
        return data.body;
    }
    return undefined;
}



interface CurrentWhatsappTaskProps extends RouteComponentProps<any> {
  taskData: Task.ITask;
  onChangeTaskStatus: (task: Task.ITask, data: Task.ITaskStatus, status: string) => void;
  logInStore?: LogIn.ILogInStore;
  teamStore?: Teams.ITeamStore;
}

interface CurrentWhatsappTaskState {
  actionInProgress?: 'whatsapp' | 'snooze' | 'skip' | 'loading' | 'done';
  hasWhatsappAccount: boolean;
  showSnoozeTaskModel: boolean;
  showReplySentimentModal: boolean;

  currentProspect?: Prospects.IProspectSearchResult;
  selectedPhoneOfProspect?: string;
}

class CurrentWhatsappTaskComp extends React.Component<CurrentWhatsappTaskProps, CurrentWhatsappTaskState> {


  constructor(props: CurrentWhatsappTaskProps) {
    super(props);
    this.state = {
      hasWhatsappAccount: false,
      showSnoozeTaskModel: false,
      showReplySentimentModal: false,

      selectedPhoneOfProspect:  this.props.taskData.prospect.phone_number || ''
    };

    this.getWhatsappSettingsApi = this.getWhatsappSettingsApi.bind(this);
    this.setSelectedPhoneOfProspect = this.setSelectedPhoneOfProspect.bind(this);
  }



  getWhatsappSettingsApi() {
    return settingsApi.getWhatsappAccountSettings()
      .then(res => {
        const whatsappAccounts = res.data.accounts;
        const whatsappSettingsExistForAccount = _.find(whatsappAccounts, (account) =>
          account.owner_id === this.props.logInStore?.accountInfo.internal_id
        );
          this.setState({ hasWhatsappAccount: !!whatsappSettingsExistForAccount })

      })



  }

  componentDidMount(): void {
    this.setState({ actionInProgress: 'loading' }, () => {
      Promise.all([prospectsApi.getSpecificProspectBasicData(this.props.taskData.prospect.id),
        this.getWhatsappSettingsApi(),
      ]).then(([prospectResponse]) => {

        this.setState({
          currentProspect: prospectResponse.data.prospect,
          actionInProgress: undefined
        })

      })
      .catch(err => {
        this.setState({actionInProgress: undefined})
      })
    })

  }

  onDoneAndProceed() {
    this.setState({
      actionInProgress: 'done'
    }, () => {

      const data: Task.IDone = {
        status_type: 'done',
        done_at: new Date(),
        done_by: this.props.logInStore?.accountInfo.internal_id
      }
      this.props.onChangeTaskStatus(this.props.taskData, data, 'done_and_proceed')
    })
  }

  setSelectedPhoneOfProspect(phone: string) {
    this.setState({selectedPhoneOfProspect: phone})
  }



  handleSend(){
    const whatsappMsgBody = getWhatsAppBody(this.props.taskData.task_data)
    if(!!this.state.selectedPhoneOfProspect){
      //encodeURIComponent will naturally handle newlines as %0A which WhatsApp Web understands
      const encodedText = encodeURIComponent(whatsappMsgBody || '')
      window.open(`https://web.whatsapp.com/send?phone=${this.state.selectedPhoneOfProspect}&text=${encodedText}`)
        this.onDoneAndProceed()
    }else{
    }
  }

  render() {
    const current_prospect = this.state.currentProspect

    return (
      <div className={`bg-sr-warning-10 rounded-[12px]`}>
            <div className=' inline-flex items-start'>

              <SRPopover
                className={classNames('!bg-white flex justify-start',this.state.hasWhatsappAccount ? 'min-w-[300px]' : 'min-w-[400px]')}
                triggerElement={
                  <SRButtonFilled
                    disable={ !!this.state.actionInProgress}

                    text='Send'
                    className=' h-[32px] mr-[8px] !px-[16px] min-w-[100px]'
                    isPrimary={true}
                  />

                }
                direction='bottom-left'
              >

                  {(this.state.hasWhatsappAccount) ?
                    <>
                        <div className='px-[5px]'>
                          {
                            !!current_prospect?.phone ?
                              <div className="p-2">
                              <div className='mb-[16px]'>
                                <div className="sr-h6 !font-[600] text sr-gray-100 mb-2">Choose phone number</div>
                                <SRSimpleSelectionDropdown
                                  options={getOptionsForSelectPhone(current_prospect)}
                                  width="default"
                                  selectedValue={this.state.selectedPhoneOfProspect || ''}
                                  handleChange={(selectedOption: ISRDropdownOption) => this.setSelectedPhoneOfProspect(selectedOption.value.toString())}
                                />
                              </div>
                              <div className="flex justify-end">
                                <SRButtonFilled
                                  text="Send"
                                  isPrimary={true}
                                  onClick={this.handleSend.bind(this)}
                                  title='Send'
                                  disable={!current_prospect?.phone || !!this.state.actionInProgress}
                                  className='mb-[8px] ml-auto w-1/3'
                                />
                              </div>


                            </div>
                              :
                              <div className='my-[10px]'>
                                Prospect phone number is not provided.
                                Update the phone number to send message.
                              </div>
                          }
                        </div>
                    </>
                    :
                    <div className='flex whitespace-pre items-center'>To send message via whatsapp, please set up a whatsapp account
                      <SRLink className='prospect-datagrid-external-link-tw text-sr-default-blue hover:text-sr-default-blue' target='_blank' to={`/dashboard/account_settings/whatsapp_accounts?tid=${this.props.logInStore?.currentTeamId}`}>
                        <span className='flex'>
                          <p> here</p>

                        </span>
                      </SRLink>
                      .
                    </div>
                }
              </SRPopover>

            </div>



      </div>
    );
  }
}

export const CurrentWhatsappTask = withRouter(
  inject("logInStore", "teamStore")(observer(CurrentWhatsappTaskComp))
);