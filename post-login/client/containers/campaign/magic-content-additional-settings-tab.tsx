import * as React from 'react';
import * as _ from 'lodash';
import { inject, observer } from 'mobx-react';
import * as campaignsApi from '../../api/campaigns';
import { Formik, Form, FormikProps } from 'formik';
import { SRButtonFilled } from '@sr/design-component';
import { Campaigns, LogIn } from '@sr/shared-product-components';
import SendingModeSelection from './sending-mode';

interface IFormikAdditionalSettingValues {
  sending_mode: 'CoPilot' | 'AutoPilot' | undefined;
}

interface IFormikAdditionalSettingErrors {
  email_priority: string;
  mark_completed_after_days: string;
  max_emails_per_day: string;
  open_tracking_enabled: string;
  click_tracking_enabled: string;
  enable_email_validation: string;
  add_prospect_to_dnc_on_opt_out: string;
  send_plain_text_email: string;
  sending_mode?: string;
}

interface IMagicContentAdditionalSettingsProps {
  campaignStore?: Campaigns.ICampaignStore;
  logInStore?: LogIn.ILogInStore;
  alertStore: Alerts.IAlertStore;
  canEditSetting: boolean;
  onUnsavedChanges: (hasChanges: boolean) => void;
}

interface IMagicContentAdditionalSettingsStates {
  isSaving?: boolean;
  hasUnsavedChanges: boolean; // New state variable
  initialValues: IFormikAdditionalSettingValues;
  error: IFormikAdditionalSettingErrors
}

class MagicContentAdditionalSettingsTabComponent extends React.Component<IMagicContentAdditionalSettingsProps, IMagicContentAdditionalSettingsStates> {

  constructor(props: IMagicContentAdditionalSettingsProps) {
    super(props);

    this.state = {
      isSaving: false,
      hasUnsavedChanges: false, // New state variable
      initialValues: this.getInitialValues(),
      error: { } as IFormikAdditionalSettingErrors
    };

    this.validateDefs = this.validateDefs.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
    this.handleFormChange = this.handleFormChange.bind(this);
  }

  validateEmailAccountSettings() {
    const campaign_email_settings = this.props.campaignStore!.getBasicInfo.settings.campaign_email_settings;
    if (!_.isEmpty(campaign_email_settings)) {
      return false;
    } else {
      return true;
    }
  }

  validateDefs(values: IFormikAdditionalSettingValues) {
    const errors = {} as IFormikAdditionalSettingErrors;
    const isMagicContentCampaign = this.props.campaignStore?.getBasicInfo.settings.campaign_type === 'magic_content'

    if(isMagicContentCampaign && values.sending_mode === undefined) {
      errors.sending_mode = 'Please select a sending mode';
    }

    this.setState({ error: errors })
    this.handleFormChange(values);
    return errors;
  }

  handleSubmit(values: IFormikAdditionalSettingValues) {
    this.setState({ isSaving: true });

    // const initialMaxEmailsPerDay = this.props.campaignStore!.getBasicInfo.settings.max_emails_per_day!;
    // const initialMarkCompletedAfterDays = this.props.campaignStore!.getBasicInfo.settings.mark_completed_after_days!;

    const campaignId = this.props.campaignStore!.getBasicInfo.id;

    const settings = this.props.campaignStore!.getBasicInfo.settings;

    const data = {
      email_priority: settings?.email_priority,
      max_emails_per_day: settings?.max_emails_per_day,
      mark_completed_after_days: settings?.mark_completed_after_days,
      open_tracking_enabled: settings?.open_tracking_enabled,
      click_tracking_enabled: settings?.click_tracking_enabled,
      send_plain_text_email: settings?.send_plain_text_email,
      sending_mode: values.sending_mode
    }

    campaignsApi.updateAdditionalSettings(campaignId, data)
      .then((response) => {
        this.setState({ isSaving: false });


        this.props.campaignStore!.updateBasicInfo(response.data.campaign);

        this.props.onUnsavedChanges(false);
      })
      .catch(() => {
        this.setState({ isSaving: false });
      })

  }

  onSubmitLimitConfirmModal() {
    this.handleSubmit({} as IFormikAdditionalSettingValues);
  }

  getInitialValues() {
    const initialSendingMode = this.props.campaignStore!.getBasicInfo.settings.sending_mode || undefined;


    const initialValues: IFormikAdditionalSettingValues = {
      sending_mode: initialSendingMode
    }

    return initialValues
  }

  handleFormChange(values: IFormikAdditionalSettingValues) {
    const { initialValues, hasUnsavedChanges } = this.state;
    if (!_.isEqual(values, initialValues) && !hasUnsavedChanges) {
      this.setState({ hasUnsavedChanges: true });
      this.props.onUnsavedChanges(true);
    } else if (_.isEqual(values, initialValues) && hasUnsavedChanges) {
      this.setState({ hasUnsavedChanges: false });
      this.props.onUnsavedChanges(false);
    }
  }

  render() {
    const canEditSetting = this.props.canEditSetting;
    const isSaving = this.state.isSaving;
    const logInStore = this.props.logInStore;
    const isMagicContentCampaign = this.props.campaignStore?.getBasicInfo.settings.campaign_type === 'magic_content'

    return (
      <div className='p-[16px] mb-[32px]'>
        <div className='flex-col overflow-y px-4 mt-6'>

          <Formik
            initialValues={this.getInitialValues()}
            validate={this.validateDefs}
            onSubmit={this.handleSubmit}
            onChange={(values: IFormikAdditionalSettingValues) => this.handleFormChange(values)}
          >
            {(props: FormikProps<IFormikAdditionalSettingValues>) => (
              <Form>
                <div className='flex-col space-y-6'>
                  {logInStore?.accountInfo.org.org_metadata.show_magic_content_steps && isMagicContentCampaign && <>

                  <div className='flex-1'>
                    <SendingModeSelection
                      value={props.values.sending_mode}
                      onChange={(mode) => props.setFieldValue('sending_mode', mode)}
                      disabled={!canEditSetting}
                      errorMessage={this.state.error.sending_mode}
                    />
                  </div>
                  <div className='text-right'>
                    <SRButtonFilled
                      text='Save Changes'
                      type='submit'
                      isPrimary={true}
                      width={"default"}
                      loading={isSaving}
                      disable={isSaving || !canEditSetting}
                    />
                  </div>
                  </>

                  }
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    );
  }
}
export const MagicContentAdditionalSettingsTab = inject('campaignStore', 'logInStore', 'alertStore')(observer(MagicContentAdditionalSettingsTabComponent));
