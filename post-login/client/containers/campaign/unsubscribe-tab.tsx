import * as React from 'react';
import { inject, observer } from 'mobx-react';
// import { Spinner } from '../../components/spinner';
// import { Form } from '../../components/form';
import * as campaignsApi from '../../api/campaigns';
import { Formik, FormikProps, Form } from 'formik';
import { SRFormRadioGroup, SRFormTextArea, SRFormToggle } from '@sr/design-component';
import { SRButtonFilled } from '@sr/design-component';
import { SRMessageBox } from '@sr/design-component';
import { classNames } from '../../utils/sr-utils';
import { SrIconTickCircle } from '@sr/design-component';
import * as _ from 'lodash';
import { Campaigns, LogIn } from '@sr/shared-product-components';
// import * as awsApi from '../../api/aws';
// import { generateImageFileName } from '../../utils/editor';
// const { Editor } = require('@tinymce/tinymce-react');


interface IFormikUnsubscribeValues {
  radioGroups: string;
  unsubscribe_text: string;
  unsubscribe_link: string;
  add_prospect_to_dnc_on_opt_out: boolean;
}

interface IErrors extends Form.IFormErrors {
  unsubscribe: string;
}

interface IUnsubscribeProps {
  campaignStore?: Campaigns.ICampaignStore;
  logInStore?: LogIn.ILogInStore;
  canEditSetting: boolean;
  onUnsavedChanges: (hasChanges: boolean) => void;
}

interface IUnsubscribeStates {
  optOutType?: 'none' | 'text' | 'link';
  isSaving?: boolean;
  initialValues: IFormikUnsubscribeValues;
  hasUnsavedChanges: boolean;
}

class UnsubscribeTabComponent extends React.Component<IUnsubscribeProps, IUnsubscribeStates> {

  constructor(props: IUnsubscribeProps) {
    super(props);

    this.state = {
      optOutType: 'text',
      isSaving: false,
      initialValues: this.getInitialValues(),
      hasUnsavedChanges: false, // Initialize local state
    };

    this.handleSubmit = this.handleSubmit.bind(this);
    this.validateDefs = this.validateDefs.bind(this);
    this.optOutTypeChange = this.optOutTypeChange.bind(this);
    // this.onSwitchChange = this.onSwitchChange.bind(this);
    this.handleFormChange = this.handleFormChange.bind(this);
  }

  validateEmailAccountSettings() {
    const campaign_email_settings = this.props.campaignStore!.getBasicInfo.settings.campaign_email_settings;

    if (!_.isEmpty(campaign_email_settings)) {
      return false;
    } else {
      return true;
    }
  }

  validateDefs(values: IFormikUnsubscribeValues) {
    const errors = {} as IErrors;
    if (values.radioGroups === 'link') {
      if (!values.unsubscribe_link) {
        errors.unsubscribe_link = 'This field is required. Please provide a valid link .eg. "To unsubscribe {{click here}}"';
      } else if (values.unsubscribe_link.indexOf('{{') === -1) {
        errors.unsubscribe_link = 'Please provide a valid link, eg. "To unsubscribe {{click here}}"';
      } else if (values.unsubscribe_link.indexOf('}}') === -1) {
        errors.unsubscribe_link = 'Please provide a valid link, eg. "To unsubscribe {{click here}}"';
      } else if (values.unsubscribe_link.indexOf('{{') > (values.unsubscribe_link.indexOf('}}') - 3)) {
        errors.unsubscribe_link = 'Please provide a valid link, eg. "To unsubscribe {{click here}}"';
      }
      //TODO - other cases
    } else if (values.radioGroups === 'text') {
      if (!values.unsubscribe_text) {
        errors.unsubscribe_text = 'Please provide unsubscribe text'
      }
    }
    this.handleFormChange(values);
    return errors;
  }

  optOutTypeChange(e: any, data: any) {
    console.log('optouttype change', data.value);
    this.setState({ optOutType: data.value });
  }

  handleSubmit(values: IFormikUnsubscribeValues) {
    this.setState({ isSaving: true });
    const campaignId = this.props.campaignStore!.getBasicInfo.id;
    const optOutIsText = (values.radioGroups !== 'link');//text or none considered as text
    const optOutMsg = (optOutIsText) ? ((values.radioGroups === 'none') ? '' : values.unsubscribe_text) : values.unsubscribe_link
    const addProspectToDncOnOptOut = values.add_prospect_to_dnc_on_opt_out;

    campaignsApi.updateOptOutSettings(campaignId, optOutIsText, optOutMsg, addProspectToDncOnOptOut)
      .then((response) => {
        this.setState({ isSaving: false });
        const linkedin_setting_uuid = this.props.campaignStore?.getBasicInfo?.settings?.linkedin_setting_uuid || ""
        const whatsapp_setting_uuid = this.props.campaignStore?.getBasicInfo?.settings?.whatsapp_setting_uuid || ""
        const sms_setting_uuid = this.props.campaignStore?.getBasicInfo?.settings?.sms_setting_uuid || ""

        this.props.campaignStore!.updateBasicInfo(response.data.campaign);

        this.props.campaignStore!.updateLinkedinSetting(linkedin_setting_uuid)
        this.props.campaignStore!.updateWhatsappSetting(whatsapp_setting_uuid)
        this.props.campaignStore!.updateSmsSetting(sms_setting_uuid)
        this.props.onUnsavedChanges(false);

      })
      .catch(() => {
        this.setState({ isSaving: false });
      })
  }

  componentDidMount() {
    const isInitialOptOutMsg = this.props.campaignStore!.getBasicInfo.settings.opt_out_is_text;
    const initialOptOutMsg = this.props.campaignStore!.getBasicInfo.settings.opt_out_msg;
    if (isInitialOptOutMsg) {
      this.setState({ optOutType: (initialOptOutMsg ? 'text' : 'none') });
    } else {
      this.setState({ optOutType: 'link' });
    }
    this.props.onUnsavedChanges(false);
  }

  handleFormChange(values: IFormikUnsubscribeValues) {
    const { initialValues, hasUnsavedChanges } = this.state;
    if (!_.isEqual(values, initialValues) && !hasUnsavedChanges) {
      this.setState({ hasUnsavedChanges: true });
      this.props.onUnsavedChanges(true);
    } else if (_.isEqual(values, initialValues) && hasUnsavedChanges) {
      this.setState({ hasUnsavedChanges: false });
      this.props.onUnsavedChanges(false);
    }
  }

  formikRef = React.createRef<FormikProps<IFormikUnsubscribeValues>>();

  getInitialValues() {

    const isInitialOptOutMsg = this.props.campaignStore!.getBasicInfo.settings.opt_out_is_text;
    const initialOptOutMsg = this.props.campaignStore!.getBasicInfo.settings.opt_out_msg || '';
    const addProspectToDncOnOptOut = this.props.campaignStore!.getBasicInfo.settings.add_prospect_to_dnc_on_opt_out || false;

    var optType = ''

    if (isInitialOptOutMsg) {
      var optType = initialOptOutMsg ? 'text' : 'none'
    } else {
      optType = 'link'
    }

    const initialValues = {
      radioGroups: optType,
      unsubscribe_text: optType == 'text' ? initialOptOutMsg : "Example - PS: If you don't want to hear from me, just let me know.",
      unsubscribe_link: optType == 'link' ? initialOptOutMsg : "Example - To unsubscribe, {{click here}}",
      add_prospect_to_dnc_on_opt_out: addProspectToDncOnOptOut
    }

    return initialValues;
  }

  getUnsubscribeOptions() {
    const options = [
      { value: 'none', displayText: 'None' },
      { value: 'text', displayText: 'Unsubscribe Text' },
      { value: 'link', displayText: 'Unsubscribe link' },
    ]

    return options;
  }

  render() {
    // const isNewCampaign = this.props.campaignStore.getIsNewCampaign;
    const isSaving = this.state.isSaving;
    // const initialIsOptOutLink = !this.props.campaignStore!.getBasicInfo.settings.opt_out_is_text;
    // const initialOptOutMsg = this.props.campaignStore!.getBasicInfo.settings.opt_out_msg ? this.props.campaignStore!.getBasicInfo.settings.opt_out_msg : "PS: If you don't want to hear from me, just let me know.";
    // const initialOptOutLink = initialIsOptOutLink ? initialOptOutMsg : 'To unsubscribe, {{click here}}';
    // const initialOptOutText = initialIsOptOutLink ? "PS: If you don't want to hear from me, just let me know." : initialOptOutMsg;
    const canEditSetting = this.props.canEditSetting;
    // const metadata = teamStore.getMetaData
    // const isMultichannel =  metadata.ff_multichannel || metadata.force_mc_flow || false
    // const width = !isMultichannel ? "default" : "fluid"

    // const TINYMCE_OPTIONS = {
    //   plugins: '', // defined below
    //   auto_focus: true,


    //   toolbar: 'fontsizeselect | fontselect | formatselect | bold italic underline forecolor backcolor | alignleft aligncenter alignright | indent outdent | bullist numlist | link unlink image emoticons | code | undo redo',
    //   // force_br_newlines: true,
    //   // force_p_newlines: false,
    //   forced_root_block: false, // Needed for 3.x
    //   // forced_root_block: 'div', // Needed for 3.x
    //   branding: false,
    //   statusbar: false,
    //   menubar: false,
    //   // fullpage_default_font_family: 'arial',
    //   fontsize_formats: '6px 7px 8px 9px 10px 11px 12px 14px 16px 18px 20px 24px 30px 36px',
    //   body_class: 'mce-body-default',
    //   // content_style: 'p {margin: 0; padding: 0} body {line-height: 1.4}',
    //   content_style: 'p {margin: 0; padding: 0} body {line-height: 1.4; font-family:arial; font-size: 14px}',
    //   // min_height: 200,
    //   autoresize_min_height: 200,
    //   autoresize_bottom_margin: 0,
    //   entity_encoding: 'raw',
    //   font_formats: 'Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino; Calibri = calibri;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings,zapf dingbats;',

    //   allow_conditional_comments: true,

    //   // REF: https://www.tiny.cloud/docs/get-started/upload-images/#rollingyourownimagehandler
    //   // REF: https://www.tiny.cloud/docs/demo/local-upload/
    //   // we override default upload handler to simulate successful upload
    //   images_upload_handler: function (blobInfo: any, success: any, failure: any) {

    //     const file = blobInfo.blob();
    //     var fileSize = file.size / 1024 / 1024; // in MB

    //     if (fileSize > 1) {

    //       failure('Maximum allowed image size is 1MB: ' + fileSize);

    //     } else {

    //       const imageFilename = generateImageFileName(0, 0, file.name);


    //       awsApi.getSignedUrl({ filename: imageFilename })
    //         .then((res: any) => {
    //           // console.log('Executing...');
    //           const URL = res.data.presigned_url;

    //           // console.log('found aws url: ', URL);

    //           const xhr = new XMLHttpRequest();
    //           xhr.open('PUT', URL);
    //           xhr.setRequestHeader('Content-Type', file.type);
    //           xhr.setRequestHeader('Content-Disposition', 'inline');
    //           xhr.send(file);
    //           xhr.addEventListener('load', () => {
    //             const linkUrl = xhr.responseURL.split('?');
    //             success(linkUrl[0]);
    //           });
    //           xhr.addEventListener('error', () => {
    //             const error = 'Unable to upload file Retry';
    //             failure(error);
    //           });
    //         })
    //         .catch((err) => {
    //           console.log('Failed to get URL');
    //           failure(err);
    //         });

    //     }

    //   },

    // };

    // TINYMCE_OPTIONS.plugins = 'link image code lists textcolor emoticons autoresize';

    return (
      <div className='p-[16px] mb-[32px]'>
        <div className="flex justify-evenly" >
          <h6>Unsubscribe Text / Link</h6>
        </div>
        <hr />

        <div className='flex-col px-4 mt-6'>

          {/* <ul className='list'>
              <li>Unsubscribe text or link will be appended to all emails at the end of the mail.</li>
              <li>It is recommended to have an unsubscribe text or link.</li>
            </ul> */}

          <div className='flex-1 flex mb-6 space-x-4'>
            <div className='flex-1'>
              <ul className={'space-y-1 text-center !list-none'}>
                <div className='flex space-x-2'><div className='w-[16px] m-auto'><SrIconTickCircle className={"text-sr-default-green !h-[16px] !w-[16px]"} /></div><li className='flex-1 sr-h7 !font-normal text-justify'>For Emails Only</li></div>
                <div className='flex space-x-2'><div className='w-[16px] m-auto'><SrIconTickCircle className={"text-sr-default-green !h-[16px] !w-[16px]"} /></div><li className='flex-1 sr-h7 !font-normal text-justify'>Unsubscribe text or link will be appended to all emails at the end of the mail.</li></div>
                <div className='flex space-x-2'><div className='w-[16px] m-auto'><SrIconTickCircle className={"text-sr-default-green !h-[16px] !w-[16px]"} /></div><li className='flex-1 sr-h7 !font-normal text-justify'>It is recommended to have an unsubscribe text or link.</li></div>
                <div className='flex space-x-2'><div className='w-[16px] m-auto'><SrIconTickCircle className={"text-sr-default-green !h-[16px] !w-[16px]"} /></div><li className='flex-1 sr-h7 !font-normal text-justify'>You can use merge tag while composing email to insert unsubscribe link. <a target='_blank' href='https://help.smartreach.io/docs/unsubscribe-linktext'> Click here </a>  To know more.</li></div>
              </ul>
            </div>
            <div className={'w-[80px] text-center m-auto'}>
              <img src="https://d3r6z7ju6qyotm.cloudfront.net/assets/2022_dec_unsub_image.svg"></img>
            </div>

          </div>

          {/* <SRMessageBox
              contentType="list"
              content={[
                {text: "Unsubscribe text or link will be appended to all emails at the end of the mail."},
                {text: "It is recommended to have an unsubscribe text or link."},
                {text: "You can use merge tag while composing email to insert unsubscribe link.", element: <li>To know more, click<a target='_blank' href='https://help.smartreach.io/docs/unsubscribe-linktext'> here.</a></li>}
              ]}
              width='fluid'
            /> */}
          {/* </div> */}
          <div className='flex-1'>
            <Formik
              initialValues={this.getInitialValues()}
              validate={this.validateDefs}
              onSubmit={this.handleSubmit}
              innerRef={this.formikRef}
            >
              {(props: FormikProps<any>) => (
                <Form>
                  <div className='space-y-1 ml-1'>
                    <SRFormRadioGroup
                      groupLabel='What do you plan to add ?'
                      groupLabelTooltip='' // we can add a tooltip if needed
                      name='radioGroups'
                      options={this.getUnsubscribeOptions()}
                      isHorizontal={true}
                    />
                    {props.values.radioGroups == "text" &&
                      <div className='mb-8'>
                        <SRFormTextArea
                          name='unsubscribe_text'
                          label='Unsubscribe text'
                          // labelTooltip="Example - PS: If you don't want to hear from me, just let me know."
                          width={'fluid'}
                          disabled={!canEditSetting}

                        />
                        <SRMessageBox
                          // header='Note'
                          content={[{ text: "The unsubscribe text appears at the bottom of the email." }]}
                          width={'fluid'}
                          type='warning'
                          isNote={true}
                        />
                      </div>}
                    {props.values.radioGroups == "link" &&
                      <div className='mb-8'>
                        <SRFormTextArea
                          name='unsubscribe_link'
                          label='Unsubscribe link'
                          width={'fluid'}
                          labelTooltip='This text with unsubscribe link appears at the bottom of the email.'
                          // labelTooltip='Example - To unsubscribe, {{click here}}'
                          disabled={!canEditSetting}
                        />
                        <SRMessageBox
                          content={
                            [
                              // {text: "This text with unsubscribe link appears at the bottom of the email."},
                              { text: "The words mentioned inside '{{ }}' will be the link to unsubscribe." }]
                          }
                          isNote={true}
                          width={'fluid'}
                          type='warning'
                        />

                        {/* </div> */}
                      </div>}

                      <div className='flex-1 flex'>
                    <div className="flex-1 space-y-1" >
                      <header className='sr-h6 !font-normal text-left'>
                        <>Add prospect who unsubscribe to Do Not Contact category</>
                      </header>
                      <p className='text-sr-subtext-grey text-justify !font-normal sr-h7'>
                        Prospects who click the Unsubscribe link will be added to 'Do Not Contact' category and won't be sent emails from any other campaign in future
                      </p>
                    </div>
                    <div className={classNames('text-right align-middle')}>
                      <SRFormToggle
                        name={'add_prospect_to_dnc_on_opt_out'}
                        disable={!canEditSetting}
                      />
                    </div>
                  </div>
                    <div className={classNames('text-right')}>
                      <SRButtonFilled
                        width='default'
                        type='submit'
                        text='Save Changes'
                        isPrimary={true}
                        loading={isSaving}
                        disable={isSaving || !canEditSetting}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>

    );
  }
}
export const UnsubscribeTab = inject('campaignStore', 'logInStore')(observer(UnsubscribeTabComponent
));
