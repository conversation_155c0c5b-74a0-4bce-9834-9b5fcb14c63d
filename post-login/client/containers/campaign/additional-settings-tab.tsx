import * as React from 'react';
import * as _ from 'lodash';
import { inject, observer } from 'mobx-react';
// import { Spinner } from '../../components/spinner';
// import { Form } from '../../components/form';
import * as campaignsApi from '../../api/campaigns';
// import { LimitConfirmationModal } from '../../components/limit-confirmation-modal';
import * as privateFeaturesAccess from '../../data/private-features-access';
// import { ARTICLES } from '../../data/constants';
import { Formik, Form, FormikProps } from 'formik';
import { SRButtonOutline, SRFormSelectDropDown, SRFormToggle, SrModal, SRToggle } from '@sr/design-component';
import { SRButtonFilled } from '@sr/design-component';
import { classNames } from '../../utils/sr-utils';
import { Campaigns, LogIn } from '@sr/shared-product-components';
// import { SRMessageBox } from '@sr/design-component';
// import { SRTooltip } from '@sr/design-component';

interface IFormikAdditionalSettingValues {
  email_priority: Campaigns.IEmailPriority;
  mark_completed_after_days: string;
  max_emails_per_day: string;
  open_tracking_enabled: boolean;
  click_tracking_enabled: boolean;
  enable_email_validation: boolean;
  add_prospect_to_dnc_on_opt_out: boolean;
  send_plain_text_email: boolean;
}

interface IFormikAdditionalSettingErrors {
  email_priority: string;
  mark_completed_after_days: string;
  max_emails_per_day: string;
  open_tracking_enabled: string;
  click_tracking_enabled: string;
  enable_email_validation: string;
  add_prospect_to_dnc_on_opt_out: string;
  send_plain_text_email: string;
}

// interface IErrors extends Form.IFormErrors {
//   email_priority: string;
//   mark_completed_after_days: string;
//   max_emails_per_day: string;
//   open_tracking_enabled: string;
//   click_tracking_enabled: string;
// }

interface IAdditionalSettingsProps {
  campaignStore?: Campaigns.ICampaignStore;
  logInStore?: LogIn.ILogInStore;
  alertStore: Alerts.IAlertStore;
  canEditSetting: boolean;
  onUnsavedChanges: (hasChanges: boolean) => void;
}

interface IAdditionalSettingsStates {
  isSaving?: boolean;
  hasUnsavedChanges: boolean; // New state variable
  initialValues: IFormikAdditionalSettingValues;
  showLimitConfirmModal?: boolean;
  open_tracking_enabled: boolean;
  show_open_tracking_modal: boolean;
  // valuesCopy?: IValues;
  allowDisablingEmailValidation: boolean;
  error: IFormikAdditionalSettingErrors
}

class AdditionalSettingsTabComponent extends React.Component<IAdditionalSettingsProps, IAdditionalSettingsStates> {

  constructor(props: IAdditionalSettingsProps) {
    super(props);

    this.state = {
      isSaving: false,
      hasUnsavedChanges: false, // New state variable
      initialValues: this.getInitialValues(),
      showLimitConfirmModal: false,
      show_open_tracking_modal: false,
      open_tracking_enabled: !!this.props.campaignStore!.getBasicInfo.settings.open_tracking_enabled,
      allowDisablingEmailValidation: _.includes(privateFeaturesAccess.allowDisablingEmailValidationForOrgIds, props.logInStore!.getAccountInfo.org.id),
      error: { send_plain_text_email: '' } as IFormikAdditionalSettingErrors
    };

    this.validateDefs = this.validateDefs.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
    this.getEmailPriorityOptions = this.getEmailPriorityOptions.bind(this);
    this.getDayCountOptions = this.getDayCountOptions.bind(this);
    this.openLimitConfirmModal = this.openLimitConfirmModal.bind(this);
    this.closeLimitConfirmModal = this.closeLimitConfirmModal.bind(this);
    this.onSubmitLimitConfirmModal = this.onSubmitLimitConfirmModal.bind(this);
    this.copy = this.copy.bind(this)
    // this.toShowLimitConfirmModal = this.toShowLimitConfirmModal.bind(this);
    this.handleFormChange = this.handleFormChange.bind(this);
  }

  validateEmailAccountSettings() {
    const campaign_email_settings = this.props.campaignStore!.getBasicInfo.settings.campaign_email_settings;
    if (!_.isEmpty(campaign_email_settings)) {
      return false;
    } else {
      return true;
    }
  }

  getEmailPriorityOptions() {
    const options = [
      { value: 'equal', displayText: 'Equally divided between opening and follow-up emails' },
      { value: 'first', displayText: 'Opening emails' },
      { value: 'followup', displayText: 'Follow-up emails' },
    ];
    return options;
  }

  getDayCountOptions() {
    let options = [];
    const startCount = 1;
    for (let i = startCount; i <= 100; i++) {
      const newOption = { id: i, name: i.toString() };
      options.push(newOption);
    }
    return options;
  }

  validateDefs(values: IFormikAdditionalSettingValues) {
    const errors = {} as IFormikAdditionalSettingErrors;

    if (parseInt(values.max_emails_per_day) > 500) {
      errors.max_emails_per_day = 'Cannot exceed limit of 500';   // Please Check it
    }
    if (values.send_plain_text_email && (values.click_tracking_enabled || values.open_tracking_enabled)) {
      errors.send_plain_text_email = 'Click tracking and Open tracking cant be enabled in plain text email';
    }

    this.setState({ error: errors })
    this.handleFormChange(values);
    return errors;
  }

  handleSubmit(values: IFormikAdditionalSettingValues) {
    this.setState({ isSaving: true });

    // const initialMaxEmailsPerDay = this.props.campaignStore!.getBasicInfo.settings.max_emails_per_day!;
    // const initialMarkCompletedAfterDays = this.props.campaignStore!.getBasicInfo.settings.mark_completed_after_days!;

    const campaignId = this.props.campaignStore!.getBasicInfo.id;
    const data: any = {
      email_priority: values.email_priority,
      max_emails_per_day: parseInt(values.max_emails_per_day),
      mark_completed_after_days: parseInt(values.mark_completed_after_days),
      open_tracking_enabled: this.state.open_tracking_enabled,//values.open_tracking_enabled,
      click_tracking_enabled: values.click_tracking_enabled,
      add_prospect_to_dnc_on_opt_out: values.add_prospect_to_dnc_on_opt_out,
      send_plain_text_email: values.send_plain_text_email,
    }

    if (this.state.allowDisablingEmailValidation) {
      data['enable_email_validation'] = values.enable_email_validation
    }

    campaignsApi.updateAdditionalSettings(campaignId, data)
      .then((response) => {
        this.setState({ isSaving: false });

        const linkedin_setting_uuid = this.props.campaignStore?.getBasicInfo?.settings?.linkedin_setting_uuid || ""
        const whatsapp_setting_uuid = this.props.campaignStore?.getBasicInfo?.settings?.whatsapp_setting_uuid || ""
        const sms_setting_uuid = this.props.campaignStore?.getBasicInfo?.settings?.sms_setting_uuid || ""

        this.props.campaignStore!.updateBasicInfo(response.data.campaign);

        this.props.campaignStore!.updateLinkedinSetting(linkedin_setting_uuid)
        this.props.campaignStore!.updateWhatsappSetting(whatsapp_setting_uuid)
        this.props.campaignStore!.updateSmsSetting(sms_setting_uuid)
        this.props.onUnsavedChanges(false);
      })
      .catch(() => {
        this.setState({ isSaving: false });
      })

  }

  /*
  toShowLimitConfirmModal(values: IValues) {
    const isNewCampaign = this.props.campaignStore!.getIsNewCampaign;
    const initialMaxEmailsPerDay = this.props.campaignStore!.getBasicInfo.settings.max_emails_per_day;
    const isLimitIncreasedByMoreThan10 = (parseInt(values.max_emails_per_day) - (initialMaxEmailsPerDay || 0)) > 10;

    if (!isNewCampaign && isLimitIncreasedByMoreThan10) {
      this.setState({ valuesCopy: values }, () => {
        this.openLimitConfirmModal();
      })
    } else {
      this.handleSubmit(values);
    }
  }*/

  openLimitConfirmModal() {
    this.setState({ showLimitConfirmModal: true });
  }

  closeLimitConfirmModal() {
    this.setState({ showLimitConfirmModal: false });
  }

  onSubmitLimitConfirmModal() {
    this.closeLimitConfirmModal();
    this.handleSubmit({} as IFormikAdditionalSettingValues);
  }

  copy(valueToBeCopied: string) {
    navigator.clipboard.writeText(valueToBeCopied)
    this.props.alertStore.pushAlert({ status: 'success', message: 'campaign uuid is successfully copied!' });
  }

  getInitialValues() {
    const campaignStore = this.props.campaignStore
    const emailPriority = campaignStore!.getBasicInfo.settings.email_priority;
    const initialOpenTrackingEnabled = campaignStore!.getBasicInfo.settings.open_tracking_enabled;
    const initialClickTrackingEnabled = campaignStore!.getBasicInfo.settings.click_tracking_enabled;
    const initialAddProspectToDncOnOptOut = campaignStore!.getBasicInfo.settings.add_prospect_to_dnc_on_opt_out || false;
    const initialEmailValidationEnabled = campaignStore!.getBasicInfo.settings.enable_email_validation!;
    const initialMaxEmailsPerDay = this.props.campaignStore!.getBasicInfo.settings.max_emails_per_day;
    const initialMarkCompletedAfterDays = this.props.campaignStore!.getBasicInfo.settings.mark_completed_after_days;
    const initialSendPlainTextEmail = this.props.campaignStore!.getBasicInfo.settings.send_plain_text_email ? this.props.campaignStore!.getBasicInfo.settings.send_plain_text_email : false


    const initialValues: IFormikAdditionalSettingValues = {
      email_priority: emailPriority!,
      click_tracking_enabled: initialClickTrackingEnabled!,
      open_tracking_enabled: initialOpenTrackingEnabled!,
      add_prospect_to_dnc_on_opt_out: initialAddProspectToDncOnOptOut,
      enable_email_validation: initialEmailValidationEnabled,
      mark_completed_after_days: initialMarkCompletedAfterDays!.toString(),
      max_emails_per_day: initialMaxEmailsPerDay!.toString(),
      send_plain_text_email: initialSendPlainTextEmail,
    }

    return initialValues
  }

  handleFormChange(values: IFormikAdditionalSettingValues) {
    const { initialValues, hasUnsavedChanges } = this.state;
    if (!_.isEqual(values, initialValues) && !hasUnsavedChanges) {
      this.setState({ hasUnsavedChanges: true });
      this.props.onUnsavedChanges(true);
    } else if (_.isEqual(values, initialValues) && hasUnsavedChanges) {
      this.setState({ hasUnsavedChanges: false });
      this.props.onUnsavedChanges(false);
    }
  }

  render() {
    // const isNewCampaign = this.props.campaignStore.getIsNewCampaign;
    const canEditSetting = this.props.canEditSetting;
    const isSaving = this.state.isSaving;
    const logInStore = this.props.logInStore;
    const show_send_plain_text_email = logInStore?.accountInfo.org.org_metadata.show_send_plain_text_email

    return (
      <div className='p-[16px] mb-[32px]'>
        <div className="flex justify-evenly" >
          <h6>Additional Settings</h6>
        </div>
        <hr />

        <div className='flex-col overflow-y px-4 mt-6'>

          <Formik
            initialValues={this.getInitialValues()}
            validate={this.validateDefs}
            onSubmit={this.handleSubmit}
            onChange={(values: IFormikAdditionalSettingValues) => this.handleFormChange(values)}
          >
            {(props: FormikProps<IFormikAdditionalSettingValues>) => (
              <Form>
                <div className='flex-col space-y-6'>
                  <div className='flex-1 '>
                    <SRFormSelectDropDown
                      name={'email_priority'}
                      label='Email Priority'
                      width={'fluid'}
                      options={this.getEmailPriorityOptions()}
                      placeholder='Select an email priority'
                      disabled={!canEditSetting}
                    />
                  </div>
                  {/* <div className='flex-1'>
                      <SRMessageBox
                        content={
                          [{text: !isNull ? "Maximum emails that can be sent from this email account is "+`${emailAccountSendingLimit}` : "",
                          element: <li className='sr-h7 inline-block !font-normal'>For email account level limits, <a href={'/dashboard/account_settings/email_accounts?tid=' + currentTid} target='_blank'> Click here</a>.</li>}
                          ]}
                          isNote={!isNull}
                          type='warning'
                          width='fluid'
                        />
                        <SRFormInput
                            label='Max Emails Per Day'
                            labelTooltip='Specify how many mails should be sent from a campaign per day, at the maximum.'
                            name={'max_emails_per_day'}
                            width={'fluid'}
                            disabled={!canEditSetting}
                        />
                    </div> */}
                  {show_send_plain_text_email &&
                    <>
                      <div className='flex-1 flex'>
                        <div className="flex-1 space-y-1" >
                          <header className='sr-h6 !font-normal text-left'>
                            Enable Plain text Email
                          </header>
                          <p className='text-sr-subtext-grey text-justify !font-normal sr-h7'>
                            This will send plain text email without links or HTML text Click tracking and open tracking cant be on
                          </p>
                        </div>
                        <div className={classNames('text-right align-middle')}>
                          <SRFormToggle
                            name={'send_plain_text_email'}
                            disable={!canEditSetting}
                          />
                        </div>
                      </div>

                      {this.state.error.send_plain_text_email &&
                        <div>
                          <p className='text-sr-default-red text-justify !font-normal sr-h7'>
                            {this.state.error.send_plain_text_email}
                          </p>
                        </div>}
                    </>
                  }
                  <div className='flex-1 flex'>
                    <div className="flex-1 space-y-1" >
                      <header className='sr-h6 !font-normal text-left'>
                        Enable Click Tracking
                      </header>
                      <p className='text-sr-subtext-grey text-justify !font-normal sr-h7'>
                        Click tracking is used to analyze where users click in an email and how long they spend interacting with the content
                      </p>
                    </div>
                    <div className={classNames('text-right align-middle')}>
                      <SRFormToggle
                        name={'click_tracking_enabled'}
                        disable={!canEditSetting}
                      />
                    </div>
                  </div>
                  <div className='flex-1 flex '>
                    <div className="flex-1 space-y-1" >
                      <header className='sr-h6 !font-normal text-left'>
                        Enable Open Tracking
                      </header>
                      <p className='inline text-sr-subtext-grey text-justify !font-normal sr-h7'>
                        Open tracking lets you see if your contacts have opened your sent email campaigns. <span className='text-sr-warning-60'> Gmail's recent update discourages and flags open tracking</span>
                      </p>
                    </div>
                    <div className={classNames('text-right align-middle')}>
                      <SRToggle name={'open_tracking_enabled'}
                        value={this.state.open_tracking_enabled}
                        handleChange={() => {
                          if (this.state.open_tracking_enabled) { this.setState({ open_tracking_enabled: !this.state.open_tracking_enabled }) }
                          else { this.setState({ show_open_tracking_modal: true }) }
                        }} />
                      {/* <SRFormToggle
                        name={'open_tracking_enabled'}
                        disable={!canEditSetting}
                      /> */}
                    </div>
                  </div>
                  <div className='flex-1 p-3 text-[12px] text-center font-sourcesanspro bg-sr-header-grey rounded-md'>
                    To learn more about open tracking and click tracking, <a href='https://help.smartreach.io/docs/track-opensclicks'> Click here</a>
                  </div>
                  {/* <div className='flex-1 flex'>
                    <div className="flex-1 space-y-1" >
                      <header className='sr-h6 !font-normal text-left'>
                        <>Add prospect who unsubscribe to Do Not Contact category</>
                      </header>
                      <p className='text-sr-subtext-grey text-justify !font-normal sr-h7'>
                        Prospects who click the Unsubscribe link will be added to 'Do Not Contact' category and won't be sent emails from any other campaign in future
                      </p>
                    </div>
                    <div className={classNames('text-right align-middle')}>
                      <SRFormToggle
                        name={'add_prospect_to_dnc_on_opt_out'}
                        disable={!canEditSetting}
                      />
                    </div>
                  </div> */}
                  {this.state.allowDisablingEmailValidation &&
                    <div className='flex-1 flex'>
                      <div className="flex-1 space-y-1" >
                        <header className='!font-normal text-left sr-h6'>
                          Enable Email Validation
                        </header>
                        <p className='text-sr-subtext-grey text-justify !font-normal sr-h7'>
                          Email validation is enabled by default. You can not disable this if you are using SMTP-based from-email address for sending this campaign's emails.
                        </p>
                      </div>
                      <div className={classNames('text-right align-middle')}>
                        <SRFormToggle
                          name={'enable_email_validation'}
                          disable={!canEditSetting}
                        />
                      </div>
                    </div>}

                  <div className='text-right'>
                    <SRButtonFilled
                      text='Save Changes'
                      type='submit'
                      isPrimary={true}
                      width={"default"}
                      loading={isSaving}
                      disable={isSaving || !canEditSetting}
                    />
                  </div>
                </div>
              </Form>
            )}
          </Formik>
        </div>
        {
          this.state.show_open_tracking_modal && !this.state.open_tracking_enabled && <SrModal
            title={"Warning"}
            content={
              <div className='mt-4 flex flex-col gap-4'>
                <div className='inline'>
                  Gmail's recent update discourages and flags open tracking. We advise you to disable open tracking across all campaigns and use reply rate or click through rate as an engagement metric.
                  <a href={"https://help.smartreach.io/docs/google-policy-update-disable-open-tracking-for-better-deliverability"} className='ml-2'>Learn More</a>
                </div>
                <div className='flex justify-end items-end gap-4'>
                  <SRButtonFilled text="Continue with open tracking" isNegative onClick={() => { this.setState({ show_open_tracking_modal: false, open_tracking_enabled: true }) }} />
                  <SRButtonOutline text="close" isPrimary className='px-8' onClick={() => { this.setState({ show_open_tracking_modal: false }) }} />
                </div>
              </div>
            }
            size='small'
            onClose={() => { this.setState({ show_open_tracking_modal: false }) }}
          />
        }
      </div>
    );
  }
}
export const AdditionalSettingsTab = inject('campaignStore', 'logInStore', 'alertStore')(observer(AdditionalSettingsTabComponent));
