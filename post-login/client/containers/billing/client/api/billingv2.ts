// import { Sr<PERSON>erver as server }  from '../../../../../client/api/server';
import * as server from './server';

type ICreateCheckoutSessionResponse = {
  payment_gateway: "stripe",
  redirect_to: string
} | {
  payment_gateway: "fastspring",
  secure_payload: string,
  secure_key: string
}

export function createCheckoutSession(data: Settings.ICreateSubscriptionRequest) {
  return server.post<ICreateCheckoutSessionResponse>('/api/v1/billing/subscription_checkout_session', data, { hideSuccess: true });
}

export function createOneTimePlanCheckoutSession(data: Settings.ICreateOneTimePaymentRequest) {
  return server.post<ICreateCheckoutSessionResponse>('/api/v1/billing/one_time_payment_checkout_session', data, { hideSuccess: true });
}

export function changePaymentMethod() {
  return server.post<{
    redirect_to: string
  }>('/api/v1/billing/stripe_checkout_session/payment_method', {}, { hideSuccess: true });
}

export function redirectToAccountManagement() {
  return server.get<{
    url: string
  }>('/api/v1/billing/fastspring/account_management_url', { hideSuccess: true });
}

export function redirectToInvoice(billingInvoiceId: number, pgInvoiceId: string) {
  return server.get<{invoiceLink: string}>(`/api/v1/billing/invoice/${billingInvoiceId}/${pgInvoiceId}/link`, {hideSuccess: true})
}