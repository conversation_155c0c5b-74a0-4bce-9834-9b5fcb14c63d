// import { Sr<PERSON>erver as server } from '../../../../../client/api/server';
import * as server from './server';
const url = '/api/v1/billing';

interface SrLimitsForBilling {

  max_prospect_limit_org: number,
  current_prospect_sent_count_org: number,
  
  total_sending_email_accounts: number,
  current_sending_email_accounts: number,
  
  max_spam_tests: number,
  current_spam_tests_done_count: number,
  
  max_li_automation_seats: number,
  current_li_automation_seats: number,
    
  max_client_teams: number,

  max_prospects_saved: number,

  max_crm_integrations: number,

  max_calling_seats: number,

  current_active_team_inboxes: number,

  additional_prospect_credits: number,

}


export interface ICurrentPlanLimitsObj {
  plan_id: string;
  plan_display_name: string;

  plan_text_id: string;

  limits: SrLimitsForBilling;
}

interface IGetInvoicesV2 {
  invoices: Settings.IBillingInvoiceV2[];
}

interface ICouponResponse {
  coupon: string,
  total_before_discount_usd: number,
  total_discount_usd: number,
  total_after_discount_usd: number,
  total_before_discount_inr: number,
  total_discount_inr: number,
  total_after_discount_inr: number,
}

interface ICreateOrUpdateBillingProfileRequest {
  address: Settings.IBillingAccountDetails
}

interface IBillingCountries {
  country_name: string,
  country_code: string
}

interface IBillingProfile {
  id: number,
  product_id: number,
  product_org_id: string,
  first_name: string,
  last_name: string,
  company_name: string,
  customer_email: string,
  address_line1: string,
  address_line2: string,
  address_city: string,
  address_country: string,
  address_state: string,
  address_country_code: string,
  address_pincode: string,
  tax_id?: string,
  created_at: Date,
}

export interface PlanLimitsV4 {
  display_name: string;

  included_addon_quantity: number;

  selected_addon_quantity: number;

  max_usage: number;

  current_usage: number;

  addon_license_type: Settings.AddonLicenceType;
}

export interface SrFeatureCredits {
  purchased_credits: number;
  default_credits_from_base_plan?: number;
}


// FASTSPRING APIS BELOW //

export function encrptBillingDataNew(data: any) {
  return server.post(url + '/fs/encrypt_subscription_data', data, { hideSuccess: true });
}


export function getBillingProfile() {
  return server.get<{profile: IBillingProfile}>(url + '/profile', { hideSuccess: true, hideError: true });
}

export function getSupportedCountries() {
  return server.get<{countries: IBillingCountries[]}>(url + '/countries', {hideSuccess: true});
}

export function validateCoupon(data: {
  coupon_id: string;
  total_amount_in_cents: number;
  total_amount_in_paise: number;
  selected_currency: string;
}) {
  return server.post<{ coupon: ICouponResponse }>(
    url + "/stripe/check_coupon",
    data,
    { hideSuccess: true, hideError: true }
  );
}

interface IUpdateSubscriptionResponse {
  pg: Settings.ISupportedPaymentGateway;
}

interface StripeUpdateSubResponse extends IUpdateSubscriptionResponse {
  pg: "stripe";
  sub_id: string;
  client_secret: string;
  payment_intent_status: string;
}

interface FastspringUpdateSubResponse extends IUpdateSubscriptionResponse {
  pg: "fastspring";
}

export type UpdateSubscriptionResponse =
  | StripeUpdateSubResponse
  | FastspringUpdateSubResponse;

export function updateSubscriptionNewV2(
  subId: number | undefined, 
  data: Settings.IUpdateSubscriptionRequest
) {
  return server.put<UpdateSubscriptionResponse>(url + '/subscriptions/' + subId, data,{ hideSuccess: true});
}



export function createOrUpdateBillingProfile(data: ICreateOrUpdateBillingProfileRequest) {
  return server.put<{}>(url + '/address', data,{hideSuccess:true});
}


export function getInvoicesCommonV2() {
  return server.get<IGetInvoicesV2>(url + '/invoices', { hideSuccess: true, hideError: true });
}

export function creactFsAccount(countryCode: string) {
  const data = { country_code: countryCode };
  return server.post<{ account: LogIn.IAccount }>(url + '/fs/create_fs_account', data, { hideSuccess: true });
}

export function getPlanLimits() {
  return server.get<{
    org_current_plan_limit: ICurrentPlanLimitsObj;
    addon_limits: PlanLimitsV4[];
  }>(url + "/org_current_plan_limits", { hideSuccess: true });
}


export function getBilllingPlansV2() {
  return server.get<{plans: Settings.IBillingPlansObjV2}>(url + '/plans', { hideSuccess: true });
}

export function getBilllingPlansV3() {
  return server.get<{
    new_plans: Settings.BillingPlansObj;
    referral_coupon?: string;
  }>(url + "/new_plans", { hideSuccess: true });
}

export function getOneTimeBillingPlans(data: Settings.IOneTimePlanType ){
  return server.get<{plans: Settings.IBillingOneTimePlanObj }>(url + '/one_time_plans/' +data, {hideSuccess: true});
}

export function getBillingSubscriptionDetails() {
  return server.get<{subscriptions: Settings.IBillingSubscriptionDetails[]}>(url + '/subscriptions', { hideSuccess: true, hideError: true });
}

export function cancelSubscriptionV2(subId: number | string,cancellation_reason: Settings.CancellationReason) {
  return server.del<any>(url + '/subscriptions/' + subId, cancellation_reason);
}

export function getSmartReachOrgDetails() {
  return server.get<{
    org_details: Settings.ISmartReachOrgDetails
  }>(url + '/org_details', { hideSuccess: true });
}

export function getSmartReachOrgMetadata() {
  return server.get<{
    org_metadata: Settings.ISmartReachOrgMetadata
  }>(url + '/org_metadata', { hideSuccess: true });
}

export function getRemainingCallingCredits(one_time_plan_type: Settings.IOneTimePlanType) {
  return server.get<{
    remaining_credits: SrFeatureCredits;
  }>(url + "/remaining_credits/" + one_time_plan_type, { hideSuccess: true });
}




type ICreateCheckoutSessionResponse = {
  payment_gateway: "stripe",
  subscriptionId : string,
  status : string,
  invoiceStatus : string,
  invoiceId : string,
  requiresAction : boolean,
  paymentIntentClientSecret : string
} | {
  payment_gateway: "fastspring",
  secure_payload: string,
  secure_key: string
}



export function createSubscriptionNew(data: Settings.ICreateSubscriptionRequestNew) {
  return server.post<ICreateCheckoutSessionResponse>(`${url}/create-subscription-new`, data, { hideSuccess: true });
}

export function verifySubscription(data:{
  subscriptionId: string
}){
  return server.post<{ 
    subscriptionId: string,
    status: string,
    invoiceStatus: string,
    invoiceId: string,
    paymentIntentStatus: string,
    requiresAction?: boolean,
    paymentIntentClientSecret?:string,
    success ?: boolean

    }>(`${url}/verify-subscription`,data, { hideSuccess: true });
}

