import axios from 'axios';
import { alertStore } from '../../../../stores/AlertStore';
import { logInStore } from '../../../../stores/LogInStore';
import * as _ from 'lodash';
// import { logToLoggly } from '../utils/logglyjs';
import { IPINFO_ACCESS_KEY } from '../data/config';


// const SR_BILLING_BASE_URL = 'https://6b7f-103-170-201-55.ngrok-free.app';
let SR_BILLING_BASE_URL = 'http://localhost:9001';

if ((window.location.hostname === 'billing.smartreach.io')) {

  SR_BILLING_BASE_URL = 'https://billingapi.smartreach.io';

} else if (window.location.hostname === 'billing.sreml.com') {

  SR_BILLING_BASE_URL = 'https://billingapi.sreml.com';
}

export type IAPIResponse<T> = {
  data: T,
  status: 'success' | 'error',
  message: string
};

type IAPIErrorResponse = IAPIResponse<{
  error_type: string;
  param?: string;
}>;

export type IServerResponse<T> = Promise<IAPIResponse<T>>;


interface IAPIOptions {
  hideSuccess?: boolean;
  hideError?: boolean;
}

interface I_IPINFO_RESPONSE {
  ip: string;
  hostname: string;
  city: string;
  region: string;
  country: string;
  loc: string;
  org: string;
  postal: string;
  timezone: string;
}

const axiosInstance = axios.create({
  baseURL: SR_BILLING_BASE_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },

  // `withCredentials` indicates whether or not cross-site Access-Control requests
  // should be made using credentials
  withCredentials: true
});




/*
// REF: https://stackoverflow.com/a/********
// used to track slow API calls
function logSlowAPICalls(
  axiosConfig: AxiosRequestConfig,
  responseType: 'ERROR' | 'SUCCESS'
) {

  try {
    const requestEndTime = new Date().getTime();
    const requestStartTime = (axiosConfig as any).metadata.startTime;
    const timeTaken = requestEndTime - requestStartTime;

    if (timeTaken > 3000) {

      const method = axiosConfig.method;
      const url = axiosConfig.url;

      if (logInStore.getAccountInfo && (logInStore.getAccountInfo || {} as LogIn.IAccount).teams) {

        const account_id = logInStore.getAccountInfo.user_id;
        const aid = _.isUndefined(account_id) ? -1 : account_id
        const team_id = logInStore.getCurrentTeamId;
        const tid = _.isUndefined(team_id) ? -1 : team_id;
        const email = logInStore.getAccountInfo.email;
        const team_name = logInStore.getAccountInfo.teams.filter(t => t.team_id === logInStore.getCurrentTeamId)
          .map(t => t.team_name)

        const logMsg = {
          aid,
          tid,
          email,
          team_name,
          method,
          url,
          timeTaken,
          type: responseType
        };

        logToLoggly(aid, logMsg, 'SLOW API: ');

      } else {

        const logMsg = {
          method,
          url,
          timeTaken,
          type: responseType
        };

        logToLoggly(-1, logMsg, 'SLOW API: [0]');

      }

    }
  } catch (e) {
    console.error('[logSlowAPICalls] logSlowAPICalls: ', e)
  }
}
*/

// REF: https://github.com/mzabriskie/axios#interceptors
axiosInstance.interceptors.response.use(

  (response) => {

    // const axiosConfig = response.config;
    // logSlowAPICalls(axiosConfig, 'SUCCESS');

    return response;
  },

  (err) => {

    // const axiosConfig = err.response.config as AxiosRequestConfig;
    // logSlowAPICalls(axiosConfig, 'ERROR');

    if (err.response && err.response.data) {
      if (err.response.status === 401) {
        logInStore.notAuthenticated();
      } else if (err.response.status === 403) {
        redirectToValidRoute();
      }

      return Promise.reject(err.response.data);
    } else {

      const errObj: IAPIErrorResponse = {
        data: {
          error_type: 'client_error'
        },
        status: 'error',
        message: err.message
      };

      return Promise.reject(errObj);
    }
  }
);

const redirectToValidRoute = () => {
  console.log('redirect to valid route');
  alertStore.resetBannerAlerts();
  window.location.href = '/invalid_attempt'; // fixme
}


// REF: https://github.com/mzabriskie/axios#handling-errors
// function errorHandler(err: any) {

//   if (err.response && err.response.data) {

//     return err.response.data;
//   } else {

//     const errObj: IAPIErrorResponse = {
//       data: {
//         error_type: 'client_error'
//       },
//       status: 'error',
//       message: err.message
//     };

//     return errObj;
//   }

// }

const updateAlertStore = (response: any) => {
  alertStore.pushAlert({ message: response.message, status: response.status });
};

export function post<ResponseDataType>(path: string, data: Object, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  const stringifiedData = JSON.stringify(data);

  return axiosInstance
    .post(path, stringifiedData)
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );
}


export function get<ResponseDataType>(path: string, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  
  // const cachedData = localStorage.getItem(path);
  // if (cachedData) {
  //   return Promise.resolve(JSON.parse(cachedData));
  // }

  return axiosInstance
    .get(path)
    .then(

      (response) => {
        localStorage.setItem(path, JSON.stringify(response.data));
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );
}


export function put<ResponseDataType>(path: string, data: any, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  return axiosInstance
    .put(path, JSON.stringify(data))
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );
}

export function del<ResponseDataType>(path: string, data: any, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  // REF: https://github.com/mzabriskie/axios/issues/424#issuecomment-241481280
  return axiosInstance
    .request({
      url: path,
      method: 'delete',
      data: JSON.stringify(data)
    })
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );
}

export function upload<ResponseDataType>(path: string, data: any, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  const options = {
    headers: {
      'Accept': 'application/json',
      'Content-Type': undefined
    }
  };

  return axiosInstance
    .post(path, data, options)
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );

}

export function getLocation(opts?: IAPIOptions): Promise<I_IPINFO_RESPONSE> {
  return axios
    .get(`https://ipinfo.io?token=${IPINFO_ACCESS_KEY}`)
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );
}
