import * as React from 'react';
import { with<PERSON><PERSON><PERSON>, RouteComponentProps, Route, Switch, Redirect } from 'react-router-dom';
import { inject, observer } from 'mobx-react';
import * as _ from 'lodash';
// import * as authApi from '../api/auth';
// import { Spinner } from '../components/spinner';
// import { Toastr } from '../components/toastr';
import { FeedSidebar } from '../components/feed-sidebar';
import { RestrictedAccess } from '../components/restricted-access';
import { differenceToInDays } from '../utils/date';
import { dateFormat } from '../utils/date';

import * as queryString from 'query-string';
// import { LogIn } from './login/login-page';
// import { Register } from './login/register-page';
// import { MasterDashboard } from './master-dashboard'
// import { SpecificCampaign } from './campaign/old-flow/specific-campaign-page';
import { NotFoundPage } from './not-found-page';
import { SRLink, SRRedirect } from '../components/helpers';
import SideNavbar from './side-navbar';
import { setOAuthRedirect } from '../utils/localStorage';
import { ProfileSettingsPage } from './account/profile-settings-page';
import { SettingsTeamSelectionModal } from '../components/settingsTeamSelectionModal';
import { AccessBlocked } from './access-blocked';
// import { TWNotificationPopUp } from '../tw_components/tw-notification-popup';
require('../new-styles/index.scss');
// import { ThankYouPage } from './thank_you_page';

import { getAndSetFeatureFlags } from '../utils/feature_flags_related';
import { SRButtonOutline, SRButtonTonal, SRSkeleton } from '@sr/design-component';
import { SrIconCompany } from '@sr/design-component';
import { SRSearchDropdown, ISRDropdownOption } from '@sr/design-component';
import { classNames } from '../utils/sr-utils';
// import { SrSpinner } from '@sr/design-component';
import { OnBoardingPage } from './onboarding/onboarding-page';
// import { SrIconUser } from '@sr/design-component';

// import { SrInfo } from '@sr/design-component';
import AgencyEntry from './agency-entry';
import TeamsEntry from './teams-entry';
import { LogIn } from '@sr/shared-product-components';
import { Reports } from '../components/stats/reports-component';
import {getConfigKeys} from "../api/settings";
import { HelpResources } from '../components/help_resources';
import ShowBanners from './sr-global-banners';
import InspectletCode from './inspectlet';
import {inspectletSetIdentify} from '../utils/inspectlet';
import VwoCode from './vwo';
import EnableAgencyPlanModal from './enable-ageny-plan-modal';
import * as  authApi from '../api/auth';
import { UpgradePromptModal2 } from '../components/upgrade-prompt-modal-new-2';
import { BillingSettingsPage } from './billing/client/containers/account/billing-settings-page';
import { FsAccountRedirectV2 } from './billing/client/containers/account/fs-account-redirectv2';
import { InvoiceRedirect } from './billing/client/containers/account/InvoiceRedirect';
import { PlanBasedModalWrapper } from './billing/client/components/billing_v2/plan-based-modal-wrapper';
import { SubscriptionRequiredModal } from '../components/subscription-required-modal';
import BannerAlerts, { DueBanner } from '../components/banner-alerts';
import { getPusherChannelNameForBillingPlanUpdate } from '../utils/pusher-channels';
import { pusherSubscribe, pusherUnsubscribe } from '../utils/pusher';

interface IAppProps extends RouteComponentProps<any> {
  logInStore?: LogIn.ILogInStore;
  alertStore?: Alerts.IAlertStore;
  teamStore?: Teams.ITeamStore;
  configKeysStore?: ConfigKeys.IConfigKeysStore;

}

interface IAppStates {
  // makingAPICallOnAppLoad: boolean;
  showTeamSelectionForSettings: boolean;
  placingOrderFs: boolean;
  showUpgradeModal: boolean;
  upgradePromptModalMessages: string[];
  isLoadingMetadata: boolean;

  showCallBanner: boolean;
  isLoading: boolean;
  showEnableAgencyPlanModal: boolean;
  showDisableAgencyPlanModal:boolean;
  isSubmitting: boolean;

  showPricingModal: boolean;

}

// interface ICallCreditBannnerProps {

//   warning: Alerts.IWarningBanner;
//   permissions: LogIn.IPermissions;
//   isAgencyAdminView: boolean;

// }

// function CallCreditBanner(props: ICallCreditBannnerProps) {

//   return (
//     <div className='flex items-center !h-[54px] !bg-sr-warning-brown !text-white !sr-h5 !text-semibold'>
//       {/* <Icon name='warning sign' color='orange' /> */}
//       {props.warning.warning_msg}&nbsp;
//       {props.warning.add_call_credit_button ?
//         (
//           (props.permissions.manage_billing.ownership === 'all' || props.isAgencyAdminView) ?
//             <SRLink
//               to='/dashboard/billing_settings/goto_billing'
//               target='_blank'
//               className='flex justify-center items-center h-[30px] rounded bg-white text-black px-7 text-sm !font-semibold ml-4' >
//               Add Credits
//             </SRLink>
//             : 'Please ask your admin to add credits'
//         )
//         : ''
//       }
//     </div>
//   )
// }



class AppAuthenticated extends React.Component<IAppProps, IAppStates> {

  constructor(props: IAppProps) {
    super(props);
    this.state = {
      // makingAPICallOnAppLoad: true,
      showTeamSelectionForSettings: false,
      placingOrderFs: false,
      showUpgradeModal: false,
      upgradePromptModalMessages: [],
      isLoadingMetadata: false,
      showCallBanner: false,
      isLoading: true,
      showEnableAgencyPlanModal: false,
      showDisableAgencyPlanModal:false,
      isSubmitting: false,
      showPricingModal: false
    };

    this.closeUpgradeModal = this.closeUpgradeModal.bind(this);
    this.onChangeTeam = this.onChangeTeam.bind(this);

    this.openTeamSelectionForSettingsModal = this.openTeamSelectionForSettingsModal.bind(this);
    this.closeTeamSelectionForSettingsModal = this.closeTeamSelectionForSettingsModal.bind(this);
    this.isOrgOnboardingDone = this.isOrgOnboardingDone.bind(this);
    this.isProfileOnboardingDone = this.isProfileOnboardingDone.bind(this);
    this.goToThankYouPage = this.goToThankYouPage.bind(this);
    // this.goToHome = this.goToHome.bind(this);
    this.setIsLoadingMetadata = this.setIsLoadingMetadata.bind(this);
    this.checkRouteCondition = this.checkRouteCondition.bind(this);
    this.handleEnableAgencyPlan = this.handleEnableAgencyPlan.bind(this);
    this.openPricingModal = this.openPricingModal.bind(this);
    this.closePricingModal = this.closePricingModal.bind(this);
  }

  loggedOutRoutes = [
    '/',
    '/login',
    '/register'
  ];

  handleEnableAgencyPlan(enable:boolean) {
    const account: LogIn.IAccount = this.props.logInStore!.getAccountInfo;
    console.log("account flag check", account)
    this.setState({ isSubmitting: true });
    authApi.enableAgencyFeatures(enable)
      .then(response => {
        console.log('Agency features enabled:', response);
        this.setState({ showEnableAgencyPlanModal: false,showDisableAgencyPlanModal: false, isSubmitting: false })



        // Handle success (e.g., show a success message or update the UI)
        window.location.href = '/';

      })
      .catch(error => {
        console.error('Error enabling agency features:', error);
        this.setState({ isSubmitting: false });
        // Handle error (e.g., show an error message)
      });
  }

  openUpgradeModal() {
    this.setState({ showUpgradeModal: true })
  }

  closeUpgradeModal() {
    this.setState({ showUpgradeModal: false });
  }

  openPricingModal(){
    this.setState({ showPricingModal: true });
  }

  closePricingModal(){
    this.setState({ showPricingModal: false });
  }

  setIsLoadingMetadata(isLoading: boolean) {
    this.setState({ isLoadingMetadata: isLoading });
  }

  openTeamSelectionForSettingsModal() {
    this.setState({ showTeamSelectionForSettings: true })
  }

  closeTeamSelectionForSettingsModal() {
    this.setState({ showTeamSelectionForSettings: false })
  }

  openEnableAgencyPlanModal() {
    this.setState({ showEnableAgencyPlanModal: true })
  }

  closeEnableAgencyPlanModal() {
    this.setState({ showEnableAgencyPlanModal: false })
  }

  upgradePromptRelatedOps() {
    const { location } = this.props;
    const logInStore = this.props.logInStore!;

    const routeIsPricing = _.includes(location.pathname, '/pricing');

    if ((logInStore.getPlanType === 'trial') && !routeIsPricing) {
      //2 days before end of trial
      const diffInDays = differenceToInDays(logInStore.getAccountInfo.org.trial_ends_at || 0);
      console.log('diff in days', diffInDays);
      if ((diffInDays <= 2)) {
        this.setState({
          upgradePromptModalMessages: [
            'Your trial ends on ' + dateFormat(logInStore.getAccountInfo.org.trial_ends_at || '') + '. Upgrade now for uninterrupted service.'
          ],
        }, () => {
          this.openUpgradeModal();
        })
      } else if ((logInStore.getAccountInfo.org.error_code === 'prospect_limit_exceeded') && (logInStore.getAccountInfo.org.plan.plan_type !== 'paid')) {
        this.setState({
          upgradePromptModalMessages: [
            logInStore.getAccountInfo.org.error!,
          ],
        }, () => {
          this.openUpgradeModal();
        })
      }
    } else if (logInStore.getAccountInfo.org.plan.plan_type === 'inactive') {
      this.setState({
        upgradePromptModalMessages: [
          'Upgrade now to continue using your SmartReach.io account.'
        ],
      }, () => {
        this.openUpgradeModal();
      })
    }

  }

  goToThankYouPage() {
    const logInStore = this.props.logInStore!;
    const query = queryString.parse(this.props.location.search);
    this.props.history.push({
      pathname: '/dashboard/thank_you',
      search: '?' + queryString.stringify({
        ...query,
        tid: logInStore.currentTeamId
      }),
    });
  }

  // goToHome() {
  //   console.log('got to home 1');
  //   const account: LogIn.IAccount = this.props.logInStore!.getAccountInfo;
  //   const logInStore = this.props.logInStore!;
  //   const isAgencyAdmin = (account.account_type === 'agency') && (logInStore.roleIsOrgOwnerOrAgencyAdminForAgency);
  //   const query = queryString.parse(this.props.location.search);
  //   const dashboardRoute = isAgencyAdmin ? '/dashboard/teams' :(logInStore.getAccountInfo.org.org_metadata.show_quick_start_tab && !this.state.isCampaignCreated)? '/dashboard/quickstart':'/dashboard/campaigns';
  //   if (isAgencyAdmin) {
  //     logInStore.updateCurrentTeamId(0);
  //   }
  //   this.props.history.push({
  //     pathname: dashboardRoute,
  //     search: '?' + queryString.stringify({
  //       ...query,
  //       tid: logInStore.currentTeamId
  //     }),
  //   });
  // }

  componentDidMount() {
    console.log("hello app CDMOUNT: ", this.props.location, this.props.match)

    // authApi.authenticate()
    //   .then((response) => {


    getConfigKeys()
    .then((res) => {
      console.log("getting config");

      const input: ConfigKeys.ConfigKeysResponse = {
        pusher_key: res.data.config.pusher_key,
        pusher_cluster: res.data.config.pusher_cluster,
        google_oauth_client_id: res.data.config.google_oauth_client_id,
        stripe_publishable_key: res.data.config.stripe_publishable_key
      };
      return this.props.configKeysStore?.updateConfigKeys(input);
    })
    .then((resp) => {

    const account: LogIn.IAccount = this.props.logInStore!.getAccountInfo;

    const disableAnalytics: boolean = this.props.logInStore!.disableAnalytics;

    const via_csd = this.props.logInStore?.isSupportAccount ?? false;

    const query = queryString.parse(this.props.location.search);

    const logInStore = this.props.logInStore!;

    const pathName = window.location.pathname;


    //in case of oauth redirect
    const oauthRedirectState = (window.location.pathname === '/dashboard/account_settings/email_accounts' || window.location.pathname === '/dashboard/account_settings/integrations') ? query.state as string : null;
    // if(state) {
    const tidString = oauthRedirectState ? oauthRedirectState.split('___')[1] : null;
    const tidFromOAuthRedirect = parseInt(tidString as string, 10);

    //from normal-flow url
    const tidFromUrl = parseInt(query.tid as string, 10);


    const tidFromUrlExists = (_.isNull(tidFromUrl) || _.isUndefined(tidFromUrl) || _.isNaN(tidFromUrl)) ? false : true;

    //Note: permissions are accessed in children's render. So having correct aid and tid is important as a typescript error frezes the page.
    // Previously API call would throw an error. But after permissions changes, typescript error happens before API call.
    const isAgencyAdmin = (account.account_type === 'agency') && (logInStore.roleIsOrgOwnerOrAgencyAdminForAgency);
    // const isTeamAccount = response.data.account.account_type==='team';

    const teamObjIndexForTidFromUrl = _.findIndex(account.teams, (team) => { return team.team_id === tidFromUrl });
    const isTidFromUrlValid = (
      // (tidFromUrl === 0 || _.isEmpty(tidFromUrl)) &&
      (tidFromUrl === 0 || !tidFromUrlExists) &&
      isAgencyAdmin &&
      (
        _.includes(pathName, 'dashboard/teams') ||
        _.includes(pathName, 'dashboard/agency_settings') ||
        _.includes(pathName, 'dashboard/profile_settings') ||
        _.includes(pathName, 'dashboard/billing_settings')
      )
    )
      ? true
      : (_.includes(pathName, 'dashboard/profile_settings/referral_program') ? true : (teamObjIndexForTidFromUrl !== -1));
    //when not an agency even then making it true for referrals

    console.log('aid, tid from url', tidFromUrl, _.cloneDeep(this.props.location), this.props.location, query.aid);

    const tidToSend = (oauthRedirectState) ? tidFromOAuthRedirect : (tidFromUrlExists ? (isTidFromUrlValid ? tidFromUrl : undefined) : undefined);

    console.log('CDM tidToSend: ', tidToSend, isTidFromUrlValid, teamObjIndexForTidFromUrl, (teamObjIndexForTidFromUrl !== -1))
    logInStore.logIn({ accountInfo: account, disableAnalytics: disableAnalytics, tid: tidToSend, via_csd: via_csd });

    // const isAgencyAdmin = (logInStore.getCurrentTeamId === 0);

    // this.props.history.listen((location, action) => {
    //   // do whatever you need here
    //   console.log('cdmount: HISTORY change: ', location, action)
    // })



    console.log('CDM3 PUSHING', tidFromUrl, pathName, queryString.stringify({
      ...query,
      tid: logInStore.currentTeamId
    }));


    let routePathname = pathName;

    const isAccountBlocked = !!_.find(logInStore.getAccountInfo.org.errors, (err) => { return (err.error_code === 'blocked') });

    console.log("isAccountBlocked:",isAccountBlocked);
    if (isAccountBlocked) {
      routePathname = '/dashboard/access_blocked';
    } else if ((!this.isOrgOnboardingDone()) || (!this.isProfileOnboardingDone())) {
      routePathname = '/onboarding';
    }
    else if (this.checkRouteCondition(routePathname, oauthRedirectState, isTidFromUrlValid)) {
      const dashboardRoute = isAgencyAdmin ? '/dashboard/teams' : '/dashboard';
      routePathname = dashboardRoute;
      console.log(`CDM4 routePathname: ${routePathname}`);

      // NOTE:
      // 1. The oauth integration redirect from Office365 and GSuite was failing without this check
      // 2. billing_redirect should also work without aid and tid
    }

    console.log('path name', routePathname);

    this.props.history.push({
      pathname: routePathname,
      search: '?' + queryString.stringify({
        ...query,
        tid: logInStore.currentTeamId
      }),
    });

    console.log('path name', routePathname);

    this.setState({ isLoading: false });

    // this.setState({ makingAPICallOnAppLoad: false }, () => {
    //this.setState({ showOnboardingModal: (!this.isOrgOnboardingDone()) || (!this.isProfileOnboardingDone()) })
    // });

    console.log('end of authenticate success');
    if(logInStore.accountInfo.org.plan.plan_type == "trial"){
      inspectletSetIdentify(logInStore.accountInfo.email)
    }


    // NOTE: this api failure, we are not handling explicitly - user will see the error toastr though
    // })
    // .catch((err) => {
    //   console.log('authenticate fail: ', err);
    //   this.setState({ makingAPICallOnAppLoad: false });
    // });
    getAndSetFeatureFlags(this.setIsLoadingMetadata)

    const channelName = getPusherChannelNameForBillingPlanUpdate(logInStore.accountInfo.org.id);
    pusherSubscribe<LogIn.IOrg>({
      channel_name: channelName,
      event_name: "billing_plan.updated",
      on_event: (data) => {
        logInStore.updateOrg(data.event_data);

        console.log("Billing plan update: ", logInStore.getAccountInfo.org);


      }
    })

  })
  }

  componentWillUnmount() {
    const logInStore = this.props.logInStore!;
    const channelName = getPusherChannelNameForBillingPlanUpdate(logInStore.accountInfo.org.id);
    pusherUnsubscribe({
      channel_name: channelName,
    });
  }

  componentDidUpdate(prevProps: IAppProps, prevState: any) {
    console.log('app DID update');
    // (Object as any).entries(this.props).forEach(([key, val]: [any, any]) =>
    //   (prevProps as any)[key] !== val && console.log(`Prop '${key}' changed`)
    // );
    // (Object as any).entries(this.state).forEach(([key, val]: [any, any]) =>
    //   prevState[key] !== val && console.log(`State '${key}' changed`)
    // );

    if (this.props.location.pathname !== prevProps.location.pathname || this.props.location.search !== prevProps.location.search) {
      console.log('Route CHANGE from: ', prevProps.location.pathname + prevProps.location.search, ' to ', this.props.location.pathname + this.props.location.search)

    } else if (this.props.location !== prevProps.location) {
      console.log('Location obj CHANGEd: ', this.props.location, prevProps.location)
    }

    const logInStore = this.props.logInStore!;

    const isAccountBlocked = !!_.find(logInStore.getAccountInfo.org.errors, (err) => { return (err.error_code === 'blocked') });

    if (isAccountBlocked && !_.includes(this.props.location.pathname, 'blocked')) {
      const routePathname = '/dashboard/access_blocked';
      this.props.history.push(routePathname);
    } else {
      // if (this.state.makingAPICallOnAppLoad === false) {

      const isInALoggedOutRoute = _.includes(this.loggedOutRoutes, _.toLower(_.trim(this.props.location.pathname)))

      if (logInStore.getRedirectToLoginPage) {

        if (!isInALoggedOutRoute) {

          console.log('component Did update redirect to login', logInStore.getRedirectToLoginPage);
          setOAuthRedirect({ pathName: prevProps.location.pathname, search: prevProps.location.search });
          this.props.history.push('/login');
          logInStore.changeRedirectToLoginPage(false);

        } else {

          // already in a logged out route
          logInStore.changeRedirectToLoginPage(false);

        }

      } else if (logInStore.isLoggedIn) {

        const query = queryString.parse(this.props.location.search);

        //from normal-flow url
        const tidFromUrl = parseInt(query.tid as string, 10);


        const storeTid = logInStore.getCurrentTeamId;

        const isTidFromUrlDiffFromStore = tidFromUrl !== storeTid;

        console.log('CDMU2 aid reload: ', tidFromUrl, storeTid);

        if (_.isNaN(tidFromUrl)) {

          console.log('CDM1');

          this.props.history.push({
            pathname: location.pathname,
            search: queryString.stringify({
              ...query,
              tid: storeTid
            }),
          });

        } else if (

          logInStore.isLoggedIn && isTidFromUrlDiffFromStore
        ) {
          console.log('TEST DIFF aid reload: ', tidFromUrl, storeTid);
          /* Note:
             Since Component Did Mount is called before Component Did Update
             If the tidFromUrl is Valid , we update the tid in the store
             so instead of just reloading window we can use the storeTid as the value will be correct only
          */
          this.props.history.push({
            pathname: location.pathname,
            search: queryString.stringify({
              ...query,
              tid: storeTid
            }),
          });
        }

        // fixme: if above elseif path is hit, this next code will never run
        if (logInStore.getCheckForUpgradePrompt) {
          this.upgradePromptRelatedOps();
          logInStore.updateCheckForUpgradePrompt(false);
        }

      }
    }

  }

  checkRouteCondition(pathName: string, oauthRedirectState: (string | null), isTidFromUrlValid: boolean) {

    return (
      _.includes(this.loggedOutRoutes, _.toLower(_.trim(pathName))) ||
      !oauthRedirectState &&
      !isTidFromUrlValid &&
      !_.includes(pathName, 'billing_redirect') &&
      !_.includes(pathName, '/oauth_redirect') &&
      !_.includes(pathName, 'goto_billing') &&
      !_.includes(pathName, '/fs_account_redirect') &&
      !_.includes(pathName, '/tp_integrations')
    )
  }

  getTeamsDropdownOptions() {
    const accInfo = this.props.logInStore!.getAccountInfo;
    const teams = accInfo.teams;
    let options: ISRDropdownOption[] = [];
    if (accInfo.account_type === 'agency') {
      _.map(teams, (team) => {
        const option: ISRDropdownOption = {
          displayText: team.team_name,
          value: team.team_id,
          displayElement: <span>
            <span
              className={classNames(team.active ? 'bg-sr-default-green' : 'bg-sr-border-grey', 'inline-block h-3 w-3 mr-3 flex-shrink-0 rounded-full')}
            />  {team.team_name}
          </span>
        };
        options.push(option);
      });

      if (accInfo.org_role === 'owner' || accInfo.org_role === 'agency_admin') {
        const option: ISRDropdownOption = {
          displayText: 'Agency dashboard',
          value: 0,
          displayElement: <span>
            <span
              className={classNames('bg-sr-default-green', 'inline-block h-3 w-3 mr-3 flex-shrink-0 rounded-full')}
            />  Agency dashboard
          </span>
        };
        options.unshift(option);
      }
    } else {
      _.map(teams, (team) => {
        if (team.active) {
          const option: ISRDropdownOption = {
            displayText: team.team_name,
            value: team.team_id,
            displayElement: <span>
              <span
                className={classNames(team.active ? 'bg-sr-default-green' : 'bg-sr-border-grey', 'inline-block h-3 w-3 mr-3 flex-shrink-0 rounded-full')}
              />  {team.team_name}
            </span>
          };
          options.push(option);
        }
      });
    }

    return options;

  }

  onChangeTeam(selectedTeam: ISRDropdownOption) {
    const teamId = selectedTeam.value as number;
    const logInStore = this.props.logInStore!;

    // to remove feed on team change if feed is on
    logInStore.showFeed&&logInStore.updateShowFeedStatus(false);
    if (teamId === 0) {

      logInStore.updateCurrentTeamId(teamId);


      this.props.history.push({
        pathname: '/dashboard/teams',
        search: queryString.stringify({
          tid: teamId,
        })
      });
    } else {



      const currentPath = this.props.location.pathname;

      // Try and retain the same path while changing teams from the Switch View Dropdown
      let gotoPath = '/dashboard'
      if (_.includes(currentPath, '/dashboard/quickstart')) {

        gotoPath = '/dashboard/quickstart'

      } else if (_.includes(currentPath, '/dashboard/campaigns')) {

        gotoPath = '/dashboard/campaigns'

      } else if (_.includes(currentPath, '/dashboard/prospects')) {

        gotoPath = '/dashboard/prospects'

      } else if (_.includes(currentPath, '/dashboard/accounts')) {

        gotoPath = '/dashboard/accounts'

      } else if (_.includes(currentPath, '/dashboard/reports')) {

        gotoPath = '/dashboard/reports'

      } else if (_.includes(currentPath, '/dashboard/templates')) {

        gotoPath = '/dashboard/templates'

      } else if (_.includes(currentPath, '/dashboard/inbox')) {

        gotoPath = '/dashboard/inbox'

      } else if (
        _.includes(currentPath, '/dashboard/account_settings') ||
        _.includes(currentPath, '/dashboard/billing_settings') ||
        _.includes(currentPath, '/dashboard/profile_settings')
      ) {

        gotoPath = currentPath;

      }


      /**
       * NOTE: 1:01 AM, 16 Dec 2022:
       * No need to fetch teamMetadata here, because on updating loginStore below,
       * it triggers a re-render of AppEntry, and that triggers a re-mounting of AppAuthenticated.
       *
       * That means, componentDidMount of AppAuthenticated gets called, and inside that we are anyways
       * fetching and setting the team metadata.
       *
       * This needs serious refactoring.
       * There should a separate TeamEntry component and currentTeamId should be store in teamStore only.
       *
       */
      logInStore.updateCurrentTeamId(teamId);

      this.props.history.push({
        pathname: gotoPath,
        search: queryString.stringify({
          tid: teamId,
        })
      });



    }
  }

  isOrgOnboardingDone() {
    const accountInfo = this.props.logInStore!.getAccountInfo;
    return (accountInfo.org.org_metadata.is_onboarding_done);
    // return true;
    // return false;
  }

  isProfileOnboardingDone() {
    const accountInfo = this.props.logInStore!.getAccountInfo;
    return (accountInfo.account_metadata.is_profile_onboarding_done);
    // return true;
    // return false;
  }


  render() {
    const logInStore = this.props.logInStore!;
    const alertStore = this.props.alertStore!;

    // const isLoading = this.state.makingAPICallOnAppLoad;
    // const isLoggingOut = logInStore.getIsLoggingOut;
    // const alert = alertStore.getAlerts; //FIXME
    // const isLoggedIn = logInStore.getLogInStatus;
    const isTeamAdmin = logInStore.getIsTeamAdmin;
    //Billing related
    const planType = logInStore.getPlanType;
    const blockAccess = (planType === 'free') ? !isTeamAdmin : false;
    // const accInfo = logInStore.getAccountInfo;

    const isAgencyAdmin = (logInStore.getCurrentTeamId === 0);
    const dashboardRoute = isAgencyAdmin ? '/dashboard/teams' : '/dashboard';
    const showUpgradeModal = this.state.showUpgradeModal || false;
    // const totalSendingEmailAccounts = accInfo.org.counts.total_sending_email_accounts;
    // const routeKey = `${logInStore.getAccountInfo.user_id}__${logInStore.getCurrentTeamId}`;

    console.log('APP-AUTHENTICATED RENDER', this.props.location.pathname, this.props.match, 'tid:', logInStore.getCurrentTeamId);

    const permissions = logInStore.getTeamRolePermissions.permissions;
    const bannerAlerts = alertStore.getBannerAlerts;
    // const errors = logInStore.getAccountInfo.org.errors;
    // const warnings = logInStore.getAccountInfo.org.warnings;
    const orgPlan = logInStore.getAccountInfo.org.plan;

    const showOnboardingModal = (!this.isOrgOnboardingDone()) || (!this.isProfileOnboardingDone());
    const isBillingRedirectNew = _.includes(this.props.location.pathname, 'goto_billing');
    const accountType = this.props.logInStore!.getAccountInfo.account_type;
    const showPricingModal = this.state.showPricingModal;

    const removeEnforcedSubscriptionForInactiveOrg =
      logInStore.getAccountInfo.org.org_metadata
        .remove_enforced_subscription_for_inactive_org ?? false;

    const isOrgInactive = orgPlan?.plan_type === "inactive";

    const isSupportUserReq = logInStore.isSupportAccount;

    const shouldShowSubscriptionModal =
      !isSupportUserReq &&
      !removeEnforcedSubscriptionForInactiveOrg &&
      isOrgInactive &&
      !showOnboardingModal;


    // const errorsSample = [
    //   {
    //     "error_msg": "Your account is blocked. Contact support if you have any questions.",
    //     "error_code": "blocked",
    //     "error_at": *************,
    //     "upgrade_now_prompt": false,
    //   }
    // ];
    // const errors = _.concat(logInStore.getAccountInfo.org.errors, errorsSample);


    // const warningsSample = [
    //   {
    //     "warning_msg": "You have reached the monthly prospect limit (18000) on your account.",
    //     "warning_code": "prospect_limit_exceeded",
    //     "warning_at": *************,
    //     "upgrade_now_prompt": true,
    //     "new_prospects_paused_till": *************
    //   }
    // ];

    return (
      // <div key={routeKey} className='app-container'> {/* routeKey is important because all components must be re-mounted on aid, tid change */}
      //   <Toastr alert={alert} />
      //   {/* outside feedsidebar so that toastr is visible on top of modals */}

      //   {
      //     (isLoggingOut) ?
      //       <Spinner spinnerTitle={isLoggingOut ? 'logging out ..' : 'loading ..'} /> :

      //       <div className='app-contents'>

      //         {!isLoggedIn &&
      //           <div className='logged-out-app'>
      //             {/* Routes for logged out app */}
      //             <Switch>
      //               <Route exact path='/login' component={LogIn} />
      //               <Route exact path='/register' component={Register} />
      //               <SRRedirect exact from='/' to={'/login'} />
      //               <SRRedirect from='*' to={'/login'} />
      //             </Switch>
      //           </div>
      //         }


      //         {isLoggedIn &&
      <>
        {logInStore.accountInfo.org.plan.plan_type === 'trial' && <InspectletCode/>}
        {logInStore.accountInfo.org.plan.plan_type === 'trial' && <VwoCode/>}
         {this.state.isLoading && <div className='h-full m-8 rounded '>
                  <SRSkeleton height='full' width='full' variant='rectangular' colorType='primary' animationSpeed='slow' />
                  </div>}

        {!this.state.isLoading &&
          <>
            {!(isBillingRedirectNew || showOnboardingModal || showUpgradeModal) &&
              <SideNavbar
                openTeamSelectionForSettingsModal={
                  this.openTeamSelectionForSettingsModal
                }
              />
            }
            {this.state.showTeamSelectionForSettings &&
              <SettingsTeamSelectionModal
                onClose={this.closeTeamSelectionForSettingsModal}
              />
            }
            <FeedSidebar showFeed={logInStore.getShowFeedStatus}>
              <div className='main-app-segment'>
                <ShowBanners />

                {!(isBillingRedirectNew || showOnboardingModal) &&
                  <div className='h-[48px] flex items-center px-[16px] border-b border-b-sr-divider-grey'>
                    <div className='flex items-center justify-center'>
                      <div className='text-sr-default-blue inline-block mr-[8px]'>
                        <SrIconCompany className="!h-[20px] !w-[20px] text-sr-gray-80" />
                      </div>
                      <SRSearchDropdown
                        inline={true}
                        options={this.getTeamsDropdownOptions()}
                        selectedValue={logInStore.getCurrentTeamId}
                        handleChange={(data: ISRDropdownOption) => {
                          if(data.value === 'additional_option') {
                            this.openEnableAgencyPlanModal();
                          } else {
                            this.onChangeTeam(data)
                          }
                        }}
                        allowAddOption={(logInStore.accountInfo.org.settings.agency_option_show && accountType !== "agency") ? true : false}
                        dontShowAdditionalOptionWhileSearching = {true}
                        additionalOptionDisplayElement={logInStore.accountInfo.org.settings.agency_option_show ?
                         <SRButtonOutline
                          icon='sr_enable_agency_icon'
                          text = {'Enable Agency Plan'}
                          onClick={this.openEnableAgencyPlanModal}
                          width='fluid'
                          className='!text-sr-gray-100 !border-sr-gray-30 !font-sourcesanspro'
                          textBold
                        />
                        : undefined
                      }
                        dropdownButtonClassName='w-[240px]'
                        dropdownMenuClassName='w-[240px]'
                        largerFontSize
                      />
                    </div>
                    <div className='flex-1 px-[16px]'>
                      <BannerAlerts
                        bannerAlerts={bannerAlerts}
                        permissions={permissions}
                        isAgencyAdmin={isAgencyAdmin}
                        onUpgradeClick={() => this.openPricingModal()}
                      />
                    </div>
                    <div>
                      {/*May 20 2025: Referral Program entry point is hidden from the UI. We will be adding it back in the future. */}

                    <SRLink to="/dashboard/profile_settings/referral_program">
                      <SRButtonOutline
                        icon='sr_icon_gift'
                        text="Refer & Earn"
                        textBold
                        className="!px-[12px] !py-[8px] !border !border-sr-gray-40"
                        iconClassName='!text-sr-violet-50'
                      />
                      </SRLink>

                    </div>
                    <div className='get-help'>
                      <HelpResources
                      onlyIcon={false}
                      triggerElement= {
                        <SRButtonTonal
                          text='Help'
                          iconPosition='right'
                          icon='sr_icon_help'
                          isPrimary={true}
                          className='w-[140px] justify-center'

                        />}
                      />
                    </div>
                  </div>
                }
                {!!orgPlan.payment_due_invoice_link && !isBillingRedirectNew &&
                  <DueBanner
                    invoiceLink={orgPlan.payment_due_invoice_link}
                    campaignPauseDate={orgPlan.payment_due_campaign_pause_at}
                  />
                }
                <div className='rest-after-top-banner'>

                  {/* <>
                <h4> notifications.length authenticated {notifications.length}</h4>
                {_.map(notifications, (notif, index) => {
                  return (
                    <>
                      <TWNotificationPopUp notif={notif} key={index} />
                      <NotificationToastr key={index} notificationAlert={notif} />
                    </>
                  )
                })}
              </> */}
                  {this.state.isLoadingMetadata && <div className='h-full m-8 rounded'>
                  <SRSkeleton height='full' width='full' variant='rectangular' colorType='primary' animationSpeed='slow' />
                  </div>}

                  {!blockAccess && !this.state.isLoadingMetadata &&
                    <Switch>
                      {/* BROWSER EXTENSION SPECIFIC ROUTES: START */}
                      {/* <Route exact path='/extension/login' component={LogInBrowserExtension} /> */}
                      {/* BROWSER EXTENSION SPECIFIC ROUTES: END */}


                      <SRRedirect exact from='/login' to={dashboardRoute} />
                      <SRRedirect exact from='/register' to={dashboardRoute} />
                      <SRRedirect exact from='/' to={dashboardRoute} />

                      {showOnboardingModal &&
                        <Route path="/onboarding" component={OnBoardingPage} />
                      }

                      {shouldShowSubscriptionModal ?
                        <Route path='*' component={SubscriptionRequiredModal} />
                          :
                        <Switch>

                          {/* <Route exact path='/master_dashboard' component={MasterDashboard} /> */}

                          <Route exact path='/dashboard/fs_account_redirectv2' component={FsAccountRedirectV2} />
                          <Route path='/dashboard/invoice/:billingInvoiceId/:pgInvoiceId/redirect'><InvoiceRedirect /></Route>


                          {/* 
                          
                          20 Aug 2025

                          For fastspring we store the invoice link 
                          as `$dashboardDomain/dashboard/billing_settings/goto_billing`, 
                          so all the goto_billing links will be redirected to billing settings page.
                          
                          */}
                          <Redirect from='/dashboard/billing_settings/goto_billing' to='/dashboard/billing_settings' />

                          <Route path='/dashboard/billing_settings' component={BillingSettingsPage} />


                          <Route path='/dashboard/profile_settings' component={ProfileSettingsPage} />

                          {/* <Redirect exact from='/dashboard/stats' to='/dashboard/stats/prospects' /> */}
                          <Route path='/dashboard/reports' component={Reports} />

                          {isAgencyAdmin ? (
                            <AgencyEntry />
                          ) : (
                            <TeamsEntry />
                          )

                          }

                          <Route exact path='/dashboard/access_blocked' component={AccessBlocked} />

                          <Route path="*" component={NotFoundPage} />
                        </Switch>
                      }

                    </Switch>
                  }

                  {blockAccess &&
                    <RestrictedAccess />
                  }

                  { showPricingModal  &&
                    <PlanBasedModalWrapper
                      onClose={()=>{
                        this.closePricingModal()
                      }}
                      shouldReloadPageOnSuccess={true}
                      shouldDisableLowestBasePlans={false}
                    />
                  }

                  {/* {(!!_.find(errors, (err) => { return (err.error_code === 'blocked') }) && !blockAccess) &&
                          <Modal size='small' open={true} dimmer='inverted'>
                            <Modal.Content>
                              <h4>{_.find(errors, (err) => { return (err.error_code === 'blocked') })?.error_msg}</h4>
                            </Modal.Content>
                          </Modal>
                        } */}


                  {(showUpgradeModal && !shouldShowSubscriptionModal && !blockAccess && !isBillingRedirectNew) &&
                    <UpgradePromptModal2
                      onClose={this.closeUpgradeModal}
                      account={logInStore.getAccountInfo}
                      // messages={this.state.upgradePromptModalMessages}
                    // showDowngradeButton={this.state.showDowngradeButton}
                    // onClickDowngradeNow={this.onClickDowngradeNow}
                    // isDowngrading={this.state.isDowngrading || false}
                    />
                  }

                  {this.state.showEnableAgencyPlanModal &&
                    <EnableAgencyPlanModal
                      onClose={() => this.closeEnableAgencyPlanModal()}
                      isAgencyAdmin={accountType === 'agency'}
                      isSubmitting={this.state.isSubmitting}
                      onSubmit={(e) => this.handleEnableAgencyPlan(e)}
                    />
                  }



                </div>
              </div>
            </FeedSidebar>
          </>
        }
      </>
      //         }
      //       </div>
      //   }

      // </div>
    );
  }

}

export default withRouter(inject('logInStore', 'alertStore', 'teamStore','configKeysStore')(observer(AppAuthenticated)));