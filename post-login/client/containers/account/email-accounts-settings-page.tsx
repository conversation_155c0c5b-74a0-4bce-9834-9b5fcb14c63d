import * as React from "react";
import * as _ from "lodash";
import * as authAPI from "../../api/auth";
import { inject, observer } from "mobx-react";
import { withRouter, RouteComponentProps } from "react-router-dom";
import { dateFormat } from "../../utils/date";
import * as settingsApi from "../../api/settings";
import { Spinner } from "../../components/spinner";
//import { EmailSettingsCustomTrackingDomainModal } from '../../components/settings/email-settings-custom-domain-tracking-modal';
//import { EmailSettingsDkimSignatureRecordModal } from '../../components/settings/email-settings-dkim-signature-record-modal'
import { EmailProviderListModal } from "../../components/settings/email-provider-list-modal";
import { EmailSettingsModal } from "../../components/settings/email-settings-modal-v3";
//import { EmailSignatureModal } from '../../components/settings/email-signature-modal';
//import { EmailSettingsOptionsModal } from '../../components/settings/email-settings-options-modal';
import * as queryString from "query-string";
import { checkPermission } from "../../utils/permissions_related";
import * as moduleApi from "../../api/module";
import { EmailOwnerChangeModal } from "../../components/settings/email-owner-change-modal";
// import { ConfirmDeleteWithInputModal } from '../../components/common-modals/confirm-delete-with-input-modal';
import { aspErrorToastr } from "../../utils/asp_error_toastr";
import { AddTeamInboxTWModal } from "../inbox-v3/add-new-team-inbox/add-team-inbox-tw-modal";
import { EditSettingsModal } from "../../components/settings/edit-email-settings-modal";
import { AddEmailTagsModal } from "../../components/settings/add-email-tags-modal";
import {
  classNames,
  SRButtonOutline,
  SRButtonText,
  SrIconCircleTickFilled,
  SrIconClose,
  SrIconTick,
  SRInput,
  SRLabel,
  SRLabel3,
  SrModal,
  SRSpinner,
  SRTable,
} from "@sr/design-component";
import { SrIconAlert } from "@sr/design-component";
import { SRButtonFilled, SRPopover, SRTooltip } from "@sr/design-component";
// import SrTipsBanner from '../sr-tips-banner';
import { SRAvatar } from "@sr/design-component";
import {
  HelpfulErrorDetails,
  HelpfulErrorDetailsResponse,
} from "../../api/server";
//import { EmailSettingsModalV2 } from '../../components/settings/email-settings-modal-v2';
import { LogIn, Settings } from "@sr/shared-product-components";
import { EmailHealthCheckRecord } from "../../api/settings";
import {
  getPusherChannelNameForEmailHealthCheck,
  getPusherChannelNameForEmailWarmupStatus,
} from "../../utils/pusher-channels";
import { pusherSubscribe, pusherUnsubscribe } from "../../utils/pusher";
import { UploadEmailModal } from "../../components/settings/upload-email-csv-modal";
import { CONSTANTS } from "../../data/constants";
import { logInStore } from "../../stores/LogInStore";
import { GenericDeleteConfirmModalV2 } from "../../components/common-components/generic-delete-confirm-modal-v2";
import * as emailInfra from "../../api/emailInfra";

export function EmailAccountWarmupStatusCell(props: {
  status: settingsApi.EmailAccountWarmupStatus | undefined;
  emailSettingUuid: string;
  initialLoading: boolean;
  updateEmailAccountStatus(
    emailAccountStatus: settingsApi.EmailAccountWarmupStatus
  ): void;
}) {
  const [isLoading, setIsLoading] = React.useState(false);

  const finalLoading = props.initialLoading || isLoading;

  if (!props.status) {
    return (
      <SRButtonOutline
        className="rounded-full px-[16px]"
        isPrimary={true}
        text="Activate warmup"
        icon="sr_icon_warmup"
        disable={finalLoading}
        loading={finalLoading}
        width="default"
        onClick={() => {
          setIsLoading(true);
          settingsApi
            .activateEmailAccountWarmup(props.emailSettingUuid)
            .then((res) => {
              if (res.status === "success" && res.data) {
                props.updateEmailAccountStatus(res.data);
              }
            })
            .finally(() => {
              setIsLoading(false);
            });
        }}
      />
    );
  }

  switch (props.status.status) {
    case "running":
      return (
        <div className={classNames("inline-flex items-center gap-2")}>
          <div className="rounded-full self-center w-5 h-5 flex items-center">
            <SrIconCircleTickFilled className="w-[20px] h-[20px] text-white" />
          </div>
          <span className="text-sr-gray-90 font-medium">Enabled warmup</span>
        </div>
      );

    case "stopped":
      return (
        <SRButtonOutline
          text="Restart"
          className="rounded-full px-[16px]"
          isPrimary={true}
          icon="sr_icon_warmup"
          disable={finalLoading}
          loading={finalLoading}
          onClick={() => {
            setIsLoading(true);
            settingsApi
              .activateEmailAccountWarmup(props.emailSettingUuid)
              .then((res) => {
                if (res.status === "success" && res.data) {
                  props.updateEmailAccountStatus(res.data);
                }
              })
              .finally(() => {
                setIsLoading(false);
              });
          }}
        />
      );

    case "paused":
      return (
        <div className={classNames("inline-flex items-center gap-2")}>
          <div className="rounded-full self-center w-5 h-5 flex items-center">
            <SrIconClose className="w-[20px] h-[20px] text-[#C81E1E]" />
          </div>
          <span className="text-sr-gray-90 font-medium">Error occurred</span>
        </div>
      );
  }
}

function getAuthStatusColor(
  authStatus: settingsApi.EmailAuthStatus
): "green" | "grey" | "red" {
  switch (authStatus) {
    case "pass":
      return "green";

    case "none":
      return "grey";

    case "fail":
      return "red";
  }
}

function EmailAccountHealthCheckModal(props: {
  email: Settings.IEmailData;
  onClose: () => void;
}) {
  const [
    isLoadingExistingHealthCheckRecord,
    setIsLoadingExistingHealthCheckRecord,
  ] = React.useState(false);

  const [healthCheckDetails, setHealthCheckDetails] =
    React.useState<settingsApi.EmailHealthCheckRecord>({
      spf_auth_status: "none",
      dkim_auth_status: "none",
      dmarc_auth_status: "none",
      status: "pending",
    });

  const [isHealthCheckInProgress, setIsHealthCheckInProgress] =
    React.useState(false);

  function checkEmailHealth(emailSetttingId: number) {
    setIsHealthCheckInProgress(true);

    settingsApi
      .checkEmailHealth(emailSetttingId)
      .then((res) => {
        if (res.status == "success") {
          setHealthCheckDetails(res.data);
        }
      })
      .finally(() => {
        setIsHealthCheckInProgress(false);
      });
  }

  React.useEffect(() => {
    setIsLoadingExistingHealthCheckRecord(true);

    settingsApi
      .getExistingEmailHealthCheckRecord(Number(props.email.id))
      .then((res) => {
        if (res.status == "success" && res.data) {
          setHealthCheckDetails(res.data);
        }
      })
      .finally(() => {
        setIsLoadingExistingHealthCheckRecord(false);
      });
  }, [props.email.id]);

  return (
    <SrModal
      title="Email health check (SPF, DKIM and DMARC)"
      size="small"
      onClose={props.onClose}
      content={
        <>
          {isLoadingExistingHealthCheckRecord ? (
            <SRSpinner spinnerTitle="Loading..." />
          ) : (
            <div>
              <div className="flex gap-4 items-center justify-center p-8">
                <SRLabel
                  text="SPF"
                  color={getAuthStatusColor(healthCheckDetails.spf_auth_status)}
                />
                <SRLabel
                  text="DKIM"
                  color={getAuthStatusColor(
                    healthCheckDetails.dkim_auth_status
                  )}
                />
                <SRLabel
                  text="DMARC"
                  color={getAuthStatusColor(
                    healthCheckDetails.dmarc_auth_status
                  )}
                />
              </div>
              <div className="flex justify-end">
                <SRButtonFilled
                  title="Email Health Check"
                  text="Check email health"
                  onClick={() => checkEmailHealth(Number(props.email.id))}
                  loading={isHealthCheckInProgress}
                  disable={isHealthCheckInProgress}
                  isPrimary={true}
                />
              </div>
            </div>
          )}
        </>
      }
    ></SrModal>
  );
}

export function getEmailHealthCheckAuthStatusIcon(props: {
  authStatus: settingsApi.EmailAuthStatus;
}) {
  switch (props.authStatus) {
    case "pass":
      return (
        <SrIconTick className="!w-[20px] !h-[20px] text-sr-success-70 p-[1px] mr-[2px]" />
      );

    case "none":
      return (
        <SrIconAlert className="w-[18px] h-[18px] text-sr-warning-70 mr-[2px]" />
      );

    case "fail":
      return (
        <SrIconAlert className="w-[18px] h-[18px] text-sr-warning-70 mr-[2px]" />
      );
  }
}

type IValues = Form.IFormValues;

interface IEmailAccountSettingsPageProps extends RouteComponentProps<any> {
  logInStore: LogIn.ILogInStore;
  alertStore?: Alerts.IAlertStore;
  teamStore?: Teams.ITeamStore;
}

interface IEmailAccountSettingsPageStates extends Settings.ISettings {
  showDeleteConfirmModal?: boolean;
  toDeleteEmailId?: number | string;
  toDeleteEmail?: string;
  deletingEmail?: boolean;
  // showMessage?: boolean;
  isOptionsModalOpen?: boolean;
  getOAuthRedirectUrlErrorCode:
    | "gmail_not_supported"
    | "gsuite_domain_not_installed"
    | "free_email_domain_not_supported"
    | "";
  isEmailSettingsModalGsuiteSubmitLoading: boolean;

  showAddTeamInboxModal: boolean;
  newEmailAccount: string;
  newEmailSettingId: number;
  step_index?: number;

  codeUrl: string | string[];
  stateUrl: string | string[];
  isEditEmailSettingsOpen: boolean;
  isEmailHealthCheckModalOpen: boolean;
  helpful_error_details?: HelpfulErrorDetails;
  searchInput?: string;
  filtered_email_data?: Settings.IEmailData[];
  emailHeachCheckLoading: number[];
  zapmailAutoReconnectLoading: number[];
  emailHealthCheckMap: Map<number, EmailHealthCheckRecord>;
  openEmailUploadModal: boolean;
  emailAccountWarmupStatus: Map<string, settingsApi.EmailAccountWarmupStatus>;
  emailAccountWarmupStatusLoading: boolean;
  selectedEmailIds: number[];
  isBulkUpdateModalOpen: boolean;
  bulkUpdateType: "quota" | "delay" | "owner" | "delete" | "tag" | null;
  isSubmittingBulkUpdate: boolean;
  isOwnerChangeModalOpen: boolean;
  isOwnerChangeModalLoading: boolean;
  isBulkDeleteModalOpen: boolean;
  isSubmittingBulkDelete: boolean;
  isBulkTagModalOpen: boolean;
  isSubmittingBulkTag: boolean;
  bulkTagInput: string;
  isBulkTagValid: boolean;
}

class EmailAccountsSettingsPageComponent extends React.Component<
  IEmailAccountSettingsPageProps,
  IEmailAccountSettingsPageStates
> {
  constructor(props: any) {
    super(props);
    this.state = {
      emailData: [],
      isLoading: true,
      isEmailSettingsModalOpen: false,
      isEmailSettingsModalLoading: false,
      isCustomTrackingModalOpen: false,
      isCustomTrackingModalLoading: false,
      isDKIMSignatureModalOpen: false,
      isDKIMSignatureModalLoading: false,
      isDKIMSignatureModalVerifyDKIMResponse: this.getEmptyVerifyDkimData(),
      isDKIMSignatureVerified: false,
      isEmailSignatureModalOpen: false,
      isEmailSignatureModalLoading: false,
      isEmailSettingsModalGsuiteSubmitLoading: false,
      showEmailProviderList: false,
      action: "Add",
      step_index: undefined,
      selectedProviderKey: "",
      showDeleteConfirmModal: false,
      toDeleteEmailId: 0,
      toDeleteEmail: "",
      deletingEmail: false,
      // showMessage: true,
      isOptionsModalOpen: false,
      currentEmailData: this.getEmptyEmailData(),
      slectedEmailAccountDkimData: this.getEmptyDkimData(),
      getOAuthRedirectUrlErrorCode: "",

      showAddTeamInboxModal: false,
      newEmailAccount: "",
      newEmailSettingId: 0,
      isEditEmailSettingsOpen: false,
      isEmailHealthCheckModalOpen: false,

      codeUrl: "",
      stateUrl: "",
      helpful_error_details: undefined,
      searchInput: undefined,
      emailHealthCheckMap: new Map(),
      emailHeachCheckLoading: [],
      zapmailAutoReconnectLoading: [],
      filtered_email_data: undefined,
      openEmailUploadModal: false,
      emailAccountWarmupStatus: new Map(),
      emailAccountWarmupStatusLoading: false,
      selectedEmailIds: [],
      isBulkUpdateModalOpen: false,
      bulkUpdateType: null,
      isSubmittingBulkUpdate: false,
      isOwnerChangeModalOpen: false,
      isOwnerChangeModalLoading: false,
      isBulkDeleteModalOpen: false,
      isSubmittingBulkDelete: false,
      isBulkTagModalOpen: false,
      isSubmittingBulkTag: false,
      bulkTagInput: "",
      isBulkTagValid: true,
    };
    this.onCloseSmtpImap = this.onCloseSmtpImap.bind(this);
    this.onSubmitSmtpImap = this.onSubmitSmtpImap.bind(this);
    this.onCustomDomainTrackingModalSubmit =
      this.onCustomDomainTrackingModalSubmit.bind(this);
    this.onCloseCustomTrackingModal =
      this.onCloseCustomTrackingModal.bind(this);
    this.transformTimestampToDate = this.transformTimestampToDate.bind(this);
    this.onProviderSelect = this.onProviderSelect.bind(this);
    this.selectEmailProvider = this.selectEmailProvider.bind(this);
    this.closeEmailProviderListModal =
      this.closeEmailProviderListModal.bind(this);
    this.updatekeyToReRender = this.updatekeyToReRender.bind(this);
    this.onSubmitEmailSignature = this.onSubmitEmailSignature.bind(this);
    this.getServiceProviderImage = this.getServiceProviderImage.bind(this);
    this.getTableColumns = this.getTableColumns.bind(this);
    this.closeDeleteConfirmModal = this.closeDeleteConfirmModal.bind(this);
    this.deleteEmailAccount = this.deleteEmailAccount.bind(this);
    // this.closeMessage = this.closeMessage.bind(this);
    this.closeEmailSignatureModal = this.closeEmailSignatureModal.bind(this);
    this.openOptionsModal = this.openOptionsModal.bind(this);
    this.closeOptionsModal = this.closeOptionsModal.bind(this);
    this.onDKIMSignatureRecordModalSubmit =
      this.onDKIMSignatureRecordModalSubmit.bind(this);
    this.onCloseDKIMSignatureRecordModal =
      this.onCloseDKIMSignatureRecordModal.bind(this);
    this.openDKIMSignatureModal = this.openDKIMSignatureModal.bind(this);
    this.onGenerateDKIMSignatureRecord =
      this.onGenerateDKIMSignatureRecord.bind(this);
    this.onChangeOwner = this.onChangeOwner.bind(this);
    this.openAddTeamInboxModal = this.openAddTeamInboxModal.bind(this);
    this.closeAddTeamInboxModal = this.closeAddTeamInboxModal.bind(this);
    this.onHelpfulMessageDimiss = this.onHelpfulMessageDimiss.bind(this);
    this.getExistingHealthCheck = this.getExistingHealthCheck.bind(this);
    this.getEmailAccountWarmupStatus =
      this.getEmailAccountWarmupStatus.bind(this);
    this.setHealthCheckDetails = this.setHealthCheckDetails.bind(this);
    this.checkEmailHealth = this.checkEmailHealth.bind(this);
    this.openEmailUploadModal = this.openEmailUploadModal.bind(this);
    this.closeEmailUploadModal = this.closeEmailUploadModal.bind(this);
    this.updateEmailAccountStatus = this.updateEmailAccountStatus.bind(this);
    this.handleEmailSelection = this.handleEmailSelection.bind(this);
    this.openBulkUpdateModal = this.openBulkUpdateModal.bind(this);
    this.closeBulkUpdateModal = this.closeBulkUpdateModal.bind(this);
    this.submitBulkQuotaUpdate = this.submitBulkQuotaUpdate.bind(this);
    this.submitBulkDelayUpdate = this.submitBulkDelayUpdate.bind(this);
    this.openOwnerChangeModal = this.openOwnerChangeModal.bind(this);
    this.closeOwnerChangeModal = this.closeOwnerChangeModal.bind(this);
    this.openBulkDeleteModal = this.openBulkDeleteModal.bind(this);
    this.closeBulkDeleteModal = this.closeBulkDeleteModal.bind(this);
    this.submitBulkDelete = this.submitBulkDelete.bind(this);
    this.openBulkAddTagModal = this.openBulkAddTagModal.bind(this);
    this.closeBulkTagModal = this.closeBulkTagModal.bind(this);
    this.submitBulkAddTag = this.submitBulkAddTag.bind(this);
    this.reconnectEmailAccount = this.reconnectEmailAccount.bind(this);
  }

  openEmailUploadModal() {
    this.setState({ openEmailUploadModal: true });
  }

  closeEmailUploadModal() {
    this.setState({ openEmailUploadModal: false });
  }

  checkEmailHealth(emailSettingId: number) {
    this.setState(
      (prevState) => ({
        emailHeachCheckLoading: [
          ...prevState.emailHeachCheckLoading,
          emailSettingId,
        ],
      }),
      () => {
        settingsApi
          .checkEmailHealth(emailSettingId)
          .then((res) => {
            if (res.status == "success") {
              this.setHealthCheckDetails(emailSettingId, res.data);
            }
          })
          .finally(() => {
            this.setState((prevState) => ({
              emailHeachCheckLoading: prevState.emailHeachCheckLoading.filter(
                (id) => id !== emailSettingId
              ),
            }));
          });
      }
    );
  }

  onProviderSelect(
    providerKey: Settings.IEmailProviderKey,
    email_address?: string,
    isConfirmInstall?: boolean
  ) {
    console.log("page provider key", providerKey);
    this.setState({ selectedProviderKey: providerKey });

    if (providerKey === "gmailapi") {
      this.setState({ isEmailSettingsModalGsuiteSubmitLoading: true });
      authAPI
        .getOAuthRedirectUrl({
          service_provider: "google",
          confirm_install: isConfirmInstall,
          campaignId: null,
          email_type: null,
          email_setting_id: null,
          email_address: email_address,
        })
        .then(
          (res: any) => {
            this.setState({ isEmailSettingsModalGsuiteSubmitLoading: false });
            window.location.assign(res.data.redirect_to);
          },
          (error: any) => {
            const alertStore = this.props.alertStore! as Alerts.IAlertStore;
            this.setState(
              {
                isEmailSettingsModalGsuiteSubmitLoading: false,
                getOAuthRedirectUrlErrorCode: error.data.error_code,
              },
              () => {
                if (
                  this.state.getOAuthRedirectUrlErrorCode ===
                  "free_email_domain_not_supported"
                ) {
                  alertStore.pushAlert({
                    status: "error",
                    message: `Please provide a work email address. Personal email addresses are not allowed`,
                  });
                }

                return error;
              }
            );
          }
        );
    } else if (providerKey === "office365") {
      authAPI
        .getOAuthRedirectUrl({
          service_provider: "outlook",
          campaignId: null,
          email_type: null,
          email_setting_id: null,
        })
        .then(
          (res: any) => {
            window.location.assign(res.data.redirect_to);
          },
          (error: any) => {
            return error;
          }
        );
    } else {
      this.setState({
        isEmailSettingsModalOpen: true,
        action: "Add",
        showEmailProviderList: false,
      });
    }
  }

  closeEmailProviderListModal() {
    this.setState({
      getOAuthRedirectUrlErrorCode: "",
      showEmailProviderList: false,
    });
  }

  selectEmailProvider() {
    this.setState({ showEmailProviderList: true });
  }

  testOnAddSmtpImap(data: any) {
    let val: any = data;
    this.setState({ isEmailSettingsModalLoading: true });
    if (data["service_provider"] === "") {
      val.service_provider = "other";
    }
    this.setState({ isEmailSettingsModalLoading: true });
    settingsApi
      .testEmailAccountSettings(data)
      .then((res) => {
        this.setState({
          helpful_error_details: undefined,
          isEmailSettingsModalLoading: false,
          step_index: 2,
        });
      })
      .catch((err) => {
        const helpfulMessageData: HelpfulErrorDetailsResponse =
          err.data as HelpfulErrorDetailsResponse;
        if (!!helpfulMessageData.helpful_error_details) {
          this.setState({
            isEmailSettingsModalLoading: false,
            helpful_error_details: helpfulMessageData.helpful_error_details,
          });
        } else {
          this.setState({ isEmailSettingsModalLoading: false });
          aspErrorToastr(data.service_provider, err);
        }
      });
  }

  addSmtpImap(data: any) {
    settingsApi.postEmailData(data).then(
      (res) => {
        let emailData = this.state.emailData || [];
        this.checkEmailHealth(+res.data.email.id);
        emailData.push(res.data.email);
        this.setState(
          {
            emailData,
            isEmailSettingsModalLoading: false,
            newEmailAccount: res.data.email.email,
            newEmailSettingId: parseInt(String(res.data.email.id)),
          },
          () => this.setState({ showAddTeamInboxModal: true })
        );
        return this.onCloseSmtpImap();
      },
      (err) => {
        this.setState({ isEmailSettingsModalLoading: false });
        return;
      }
    );
  }

  openAddTeamInboxModal() {
    this.setState({ showAddTeamInboxModal: true });
  }

  closeAddTeamInboxModal() {
    this.setState({ showAddTeamInboxModal: false });
  }

  testUpdateSmtpImap(id: any, data: any) {
    this.setState({ isEmailSettingsModalLoading: true });

    settingsApi
      .testEmailAccountSettings(data)
      .then((res) => {
        settingsApi
          .updateBasicSettingsData(id, data)
          .then((res) => {
            let emailData = this.state.emailData || [];
            _.forEach(emailData, (email, index) => {
              if (email.id === res.data.email.id) {
                emailData[index] = res.data.email;
              }
            });

            this.setState({ emailData, isEmailSettingsModalLoading: false });
            this.onCloseEditEmailSettingsModal();
          })
          .catch((err) => {
            this.setState({ isEmailSettingsModalLoading: false });
            return err;
          });
      })
      .catch((err) => {
        this.setState({ isEmailSettingsModalLoading: false });
        aspErrorToastr(data.service_provider, err);
      });
  }

  updateEmailSettings(id: string | number, data: any) {
    this.setState({ isEmailSettingsModalLoading: true });
    settingsApi
      .updateEmailData(id, data)
      .then((res) => {
        let emailData = this.state.emailData || [];
        _.forEach(emailData, (email, index) => {
          if (email.id === res.data.email.id) {
            emailData[index] = res.data.email;
          }
        });

        this.setState({ emailData, isEmailSettingsModalLoading: false });
        this.onCloseEditEmailSettingsModal();
      })
      .catch((err) => {
        this.setState({ isEmailSettingsModalLoading: false });
        return err;
      });
  }

  setHealthCheckDetails(
    email_id: number,
    data: settingsApi.EmailHealthCheckRecord
  ) {
    const heathStatus = {
      spf_auth_status: data.spf_auth_status,
      dkim_auth_status: data.dkim_auth_status,
      dmarc_auth_status: data.dmarc_auth_status,
      status: data.status,
    };

    var map = this.state.emailHealthCheckMap;
    map.set(email_id, heathStatus);
    this.setState({
      emailHealthCheckMap: map,
    });
  }

  getExistingHealthCheck() {
    settingsApi.getAllExistingEmailHealthCheckRecord().then((res) => {
      if (res.status == "success" && res.data) {
        var reports: Map<number, EmailHealthCheckRecord> = new Map();
        res.data.map((hcr, index) => {
          const ehcr: EmailHealthCheckRecord = {
            spf_auth_status: hcr.spf_auth_status,
            dkim_auth_status: hcr.dkim_auth_status,
            dmarc_auth_status: hcr.dmarc_auth_status,
            status: hcr.status,
          };
          reports.set(hcr.email_setting_id, ehcr);
        });
        this.setState({
          emailHealthCheckMap: reports,
        });
      }
    });
  }

  getEmailAccountWarmupStatus() {
    const enable_wh_auto_connect =
      this.props.logInStore.accountInfo.org.org_metadata
        .enable_wh_auto_connect ?? false;

    if (!enable_wh_auto_connect) {
      return;
    }

    this.setState({ emailAccountWarmupStatusLoading: true });
    settingsApi
      .getEmailAccountWarmupStatus()
      .then((res) => {
        if (res.status == "success" && res.data) {
          const warmupStatus = new Map<
            string,
            settingsApi.EmailAccountWarmupStatus
          >();

          res.data.forEach((item) => {
            warmupStatus.set(item.email, item);
          });

          this.setState({ emailAccountWarmupStatus: warmupStatus });
        }
      })
      .finally(() => {
        this.setState({ emailAccountWarmupStatusLoading: false });
      });
  }

  onSubmitSmtpImap(values: IValues) {
    let val: any = values;
    const action = this.state.action || "Add";
    const currentEmailData =
      this.state.currentEmailData || this.getEmptyEmailData();
    this.setState({ isEmailSettingsModalLoading: true });
    if (values["service_provider"] === "") {
      val.service_provider = "other";
    }
    if (action === "Add") {
      this.addSmtpImap(val);
    } else {
      if (
        currentEmailData.service_provider == "gmailapi" ||
        currentEmailData.service_provider == "gmail_alias" ||
        currentEmailData.service_provider == "office365"
      ) {
        settingsApi
          .updateEmailData(currentEmailData.id, val)
          .then((res) => {
            let emailData = this.state.emailData || [];
            _.forEach(emailData, (email, index) => {
              if (email.id === res.data.email.id) {
                emailData[index] = res.data.email;
              }
            });
            this.setState({ emailData, isEmailSettingsModalLoading: false });
            this.onCloseSmtpImap();
          })
          .catch((err) => {
            this.setState({ isEmailSettingsModalLoading: false });
            return err;
          });
      } else {
        this.testUpdateSmtpImap(currentEmailData.id, val);
      }
    }
  }

  onCloseEditEmailSettingsModal() {
    this.setState({
      isEditEmailSettingsOpen: false,
      isEmailSettingsModalLoading: false,
      isDKIMSignatureModalLoading: false,
      slectedEmailAccountDkimData:
        this.getEmptyDkimData() as Settings.IEmailDKIMRecord,
      isDKIMSignatureModalVerifyDKIMResponse: this.getEmptyDkimData(),
      currentEmailData: this.getEmptyEmailData() as Settings.IEmailData,
      step_index: undefined,
    });
  }

  onCloseSmtpImap() {
    this.setState({
      helpful_error_details: undefined,
      isEmailSettingsModalOpen: false,
      isEmailSettingsModalLoading: false,
      currentEmailData: this.getEmptyEmailData() as Settings.IEmailData,
      step_index: undefined,
    });
  }

  onHelpfulMessageDimiss() {
    this.setState({ helpful_error_details: undefined });
  }

  onCustomDomainTrackingModalSubmit(
    emailSettingId: string,
    trackingDomainHost: string | null,
    subDomain: string
  ) {
    this.setState({ isEmailSettingsModalLoading: true });

    settingsApi
      .updateEmailCustomTrackingDomain(emailSettingId, {
        tracking_domain_host: trackingDomainHost,
        sub_domain: subDomain,
      })
      .then((res) => {
        let emailData = this.state.emailData || [];
        _.forEach(emailData, (email, index) => {
          if (email.id === res.data.email.id) {
            emailData[index] = res.data.email;
          }
        });
        this.setState({ emailData, isEmailSettingsModalLoading: false });
        this.onCloseCustomTrackingModal();
      })
      .catch((err) => {
        this.setState({ isEmailSettingsModalLoading: false });
        return err;
      });
  }

  onCloseCustomTrackingModal() {
    this.setState({ isEmailSettingsModalLoading: false });
    this.onCloseEditEmailSettingsModal();
  }

  openDKIMSignatureModal() {
    this.setState({
      isEditEmailSettingsOpen: true,
      isDKIMSignatureModalLoading: true,
    });
    settingsApi
      .getDKIMRecord(this.state.currentEmailData!.id)
      .then((response) => {
        if (response.data.dkim.domain) {
          this.setState({
            isDKIMSignatureModalLoading: false,
            slectedEmailAccountDkimData: response.data.dkim,
          });
        } else {
          this.setState({
            isEditEmailSettingsOpen: true,
            isDKIMSignatureModalLoading: false,
            slectedEmailAccountDkimData: response.data.dkim,
          });
        }
      });
  }

  openDKIMSignatureModalFromUrl(emailId: number) {
    const selectedEmailData = _.find(this.state.emailData, (email) => {
      return email.id === emailId;
    });
    if (selectedEmailData && selectedEmailData.service_provider === "other") {
      this.setState(
        {
          isEditEmailSettingsOpen: true,
          step_index: 6,
          isDKIMSignatureModalLoading: true,
          currentEmailData: selectedEmailData,
        },
        () => {
          settingsApi
            .getDKIMRecord(this.state.currentEmailData!.id)
            .then((response) => {
              if (response.data.dkim.domain) {
                this.setState({
                  isDKIMSignatureModalLoading: false,
                  slectedEmailAccountDkimData: response.data.dkim,
                });
              } else {
                this.setState({
                  isEditEmailSettingsOpen: true,
                  isDKIMSignatureModalLoading: false,
                  slectedEmailAccountDkimData: response.data.dkim,
                });
              }
            });
        }
      );
    } else {
      const alertStore = this.props.alertStore || ({} as Alerts.IAlertStore);
      alertStore.pushAlert({
        status: "error",
        message: `Email setting (${emailId}) not found`,
      });
    }
  }

  onGenerateDKIMSignatureRecord(emailSettingId: string) {
    this.setState({ isEmailSettingsModalLoading: true });
    settingsApi.createDKIMRecord(emailSettingId).then((res) => {
      if (res.data.dkim.domain) {
        this.setState({
          slectedEmailAccountDkimData: res.data.dkim,
          isEmailSettingsModalLoading: false,
        });
      } else {
        this.setState({
          slectedEmailAccountDkimData: res.data.dkim,
          isEmailSettingsModalLoading: false,
        });
      }
    });
  }

  onDKIMSignatureRecordModalSubmit(
    emailSettingId: string,
    domain: string | null
  ) {
    this.setState({ isEmailSettingsModalLoading: true });
    settingsApi.verifyDKIMRecord(emailSettingId).then((res) => {
      this.setState({ isEmailSettingsModalLoading: false });
      if (res.data.dkim.error) {
        this.setState({
          isDKIMSignatureModalVerifyDKIMResponse: res.data.dkim,
        });
      } else if (res.data.dkim.domain) {
        this.setState({
          isDKIMSignatureVerified: true,
          slectedEmailAccountDkimData: res.data.dkim,
        });
      }
    });
  }

  onCloseDKIMSignatureRecordModal() {
    this.setState({
      isDKIMSignatureModalLoading: false,
      slectedEmailAccountDkimData:
        this.getEmptyDkimData() as Settings.IEmailDKIMRecord,
      isDKIMSignatureModalVerifyDKIMResponse: this.getEmptyDkimData(),
      step_index: undefined,
    });
  }

  onSubmitEmailSignature(data: { signature: string }) {
    this.setState({ isEmailSignatureModalLoading: true });
    settingsApi
      .updateEmailSignature(this.state.currentEmailData!.id, data)
      .then(() => {
        this.closeEmailSignatureModal();
        this.onCloseEditEmailSettingsModal();
        this.setState({ isLoading: true });
        this.getEmailSettingsApi();
      })
      .catch(() => {
        this.setState({ isEmailSettingsModalLoading: false });
      });
  }

  closeEmailSignatureModal() {
    this.setState({ isEmailSignatureModalLoading: false });
  }

  onChangeOwner(emailAccountId: number[], newOwnerId: number) {
    this.setState({ isOwnerChangeModalLoading: true });
    moduleApi
      .changeOwner("email_accounts", emailAccountId, newOwnerId)
      .then((response) => {
        const updatedEmails = response.data.emails;
        let emailData = this.state.emailData || [];

        updatedEmails.forEach((updatedEmail) => {
          const index = emailData.findIndex(
            (email) => email.id === updatedEmail.id
          );
          if (index !== -1) {
            emailData[index] = updatedEmail;
          }
        });

        this.setState({
          selectedEmailIds: [],
          emailData,
          filtered_email_data: this.state.searchInput
            ? emailData.filter((p) =>
                p.email.includes(this.state.searchInput!.trim())
              )
            : undefined,
          isOwnerChangeModalLoading: false,
          isOwnerChangeModalOpen: false,
          isEditEmailSettingsOpen: false,
        });

        // this.getEmailSettingsApi();
      })
      .catch(() => {
        this.setState({
          isOwnerChangeModalLoading: false,
          isOwnerChangeModalOpen: false,
        });
      });
  }

  getEmptyEmailData(): Settings.IEmailData {
    const logInStore = this.props.logInStore;

    return {
      first_name: "",
      last_name: "",
      error: null,
      can_send: false,
      can_receive: false,
      created_at: 0,
      email: "",
      email_address_host: "",
      id: 0,
      uuid: "",
      quota_per_day: 100,
      oauth2_enabled: false,
      service_provider: "",

      min_delay_seconds:
        this.props.logInStore?.getDefaultLowerLimitForEmailDelay()!,
      max_delay_seconds:
        this.props.logInStore?.getDefaultUpperLimitForEmailDelay()!,

      smtp_username: "",
      smtp_password: "",
      smtp_host: "",
      smtp_port: null,

      imap_username: "",
      imap_password: "",
      imap_host: "",
      imap_port: null,

      owner_id: logInStore.getAccountInfo.internal_id,
      campaign_use_status_for_email_setting: "is_not_assigned_to_any_campaign",
      can_auto_start_warmup: false,
      can_auto_reconnect_via_oauth: false,
    };
  }

  getEmptyDkimData(): Settings.IEmailDKIMRecord {
    return {
      domain: "",
      record: "",
      selector: "",
      active: false,
    };
  }

  getEmptyVerifyDkimData(): Settings.IEmailDKIMRecord {
    return {
      domain: "",
      record: "",
      selector: "",
      active: false,
      error: "",
    };
  }

  getEmailSettingsApi() {
    this.setState({ isLoading: true });
    settingsApi.getListEmailData().then(
      (response) => {
        let emailData: Settings.IEmailData[] =
          response.data.emails || this.getEmptyEmailData();
        this.setState(
          { emailData, isLoading: false, selectedEmailIds: [] },
          () => {
            const query = queryString.parse(this.props.location.search);

            const eid = query.eid;
            const did = query.did;

            this.getExistingHealthCheck();
            this.getEmailAccountWarmupStatus();
            console.log("api email", "edi:" + eid, "did:" + did);
            if (eid) {
              this.openEmailSettingsModalFromUrl(parseInt(eid as string));
            } else if (did) {
              this.openDKIMSignatureModalFromUrl(parseInt(did as string));
            }
          }
        );
        if (this.state.currentEmailData) {
          this.setState({
            currentEmailData: emailData.find(
              (e) => e.id === this.state.currentEmailData?.id
            ),
          });
        }
      },
      (err) => {
        // TODO, on error
      }
    );
  }

  updatekeyToReRender(key: Settings.IEmailProviderKey) {
    this.setState({ selectedProviderKey: key });
  }

  transformTimestampToDate(timestamp: any) {
    return dateFormat(timestamp);
  }

  getServiceProviderImage(key: Settings.IEmailProviderKey) {
    let imgPath = "";
    if (key === "gmailapi") {
      imgPath = "../../assets/gmail.png";
    } else if (key === "exchange") {
      imgPath = "../../assets/ms-exchange.png";
    } else if (key === "mailgun") {
      imgPath = "../../assets/mailgun.png";
    } else {
      imgPath = "../../assets/smtp-imap.png";
    }
    return imgPath;
  }

  // closeMessage() {
  //   this.setState({ showMessage: false });
  // }

  openDeleteConfirmModal(emailId: number | string, emailAd: string) {
    this.setState({
      showDeleteConfirmModal: true,
      toDeleteEmailId: emailId,
      toDeleteEmail: emailAd,
    });
  }

  closeDeleteConfirmModal() {
    this.setState({
      showDeleteConfirmModal: false,
      toDeleteEmailId: 0,
      toDeleteEmail: "",
    });
  }

  deleteEmailAccount() {
    this.setState({ deletingEmail: true });
    settingsApi
      .deleteEmailAccount(this.state.toDeleteEmailId || 0)
      .then((response) => {
        this.setState({ deletingEmail: false });
        this.closeDeleteConfirmModal();
        this.getEmailSettingsApi();
      })
      .catch(() => {
        this.setState({ deletingEmail: false });
        this.closeDeleteConfirmModal();
      });
  }

  openOptionsModal(emailData: Settings.IEmailData) {
    this.setState({
      isEditEmailSettingsOpen: true,
      currentEmailData: emailData,
    });
  }

  closeOptionsModal() {
    this.setState({ isEditEmailSettingsOpen: false });
  }

  getTableColumns() {
    let tableColumns: {
      name: string | JSX.Element;
      key: string;
      popup?: string;
      width: any;
    }[] = [
      {
        name: (
          <input
            type="checkbox"
            checked={
              this.state.selectedEmailIds.length ===
              (this.state.filtered_email_data || this.state.emailData || [])
                .length
            }
            onChange={(e) => {
              const isChecked = e.target.checked;
              const emailIds = (
                this.state.filtered_email_data ||
                this.state.emailData ||
                []
              ).map((email) => +email.id);
              this.setState({
                selectedEmailIds: isChecked ? emailIds : [],
              });
            }}
            className="w-4 h-4 text-sr-default-blue bg-gray-100 border-gray-300 rounded focus:ring-sr-default-blue"
          />
        ),
        key: "checkbox",
        width: 1,
      },
      { name: "Email address and name", key: "email_and_name", width: 3 },
      { name: "Provider", key: "service_provider", width: 2 },
      { name: "Owner", key: "owner_name", width: 1 },
      { name: "Send / Receive", key: "can_send_receive", width: 1 },
      { name: "Daily limit", key: "quota_per_day", width: 2 },
    ];

    const show_rms_ip_in_frontend =
      this.props.logInStore.accountInfo.org.org_metadata
        .show_rms_ip_in_frontend;
    if (show_rms_ip_in_frontend) {
      tableColumns.push({ name: "IP", key: "rms_ip", width: 2 });
    }

    tableColumns.push({ name: "Health check", key: "ehc", width: 3 });

    const permissions = logInStore.getTeamRolePermissions.permissions;

    const canEditTeamConfig = checkPermission(permissions.edit_team_config);

    const enable_wh_auto_connect =
      this.props.logInStore.accountInfo.org.org_metadata
        .enable_wh_auto_connect ?? false;
    if (enable_wh_auto_connect && canEditTeamConfig) {
      tableColumns.push({ name: "Warm-up", key: "auto_connect", width: 2 });
    }

    tableColumns.push({ name: "", key: "actions", width: 2 });
    return tableColumns;
  }

  openEmailSettingsModalFromUrl(emailId: number) {
    const selectedEmailData = _.find(this.state.emailData, (email) => {
      return email.id === emailId;
    });
    console.log("open email settings modal from url", selectedEmailData);
    if (selectedEmailData) {
      this.setState({
        isEditEmailSettingsOpen: true,
        currentEmailData: selectedEmailData,
        action: "Edit",
      });
    }
  }

  componentDidUnmount() {
    const teamId = this.props.logInStore.getCurrentTeamId;

    const channelName = getPusherChannelNameForEmailHealthCheck(teamId);

    pusherUnsubscribe({ channel_name: channelName });

    const emailWarmupStatuschannelName =
      getPusherChannelNameForEmailWarmupStatus({
        teamId: this.props.logInStore.currentTeamId,
      });

    pusherUnsubscribe({
      channel_name: emailWarmupStatuschannelName,
    });

    // Remove event listener for clicking outside dropdown
    document.removeEventListener("mousedown", this.handleClickOutside);
  }

  componentDidMount() {
    const query = queryString.parse(this.props.location.search);

    console.log("DOUBLECODECALL email account settings page mounted", query);

    const emailWarmupStatuschannelName =
      getPusherChannelNameForEmailWarmupStatus({
        teamId: this.props.logInStore.currentTeamId,
      });

    pusherSubscribe<settingsApi.EmailAccountWarmupStatus>({
      channel_name: emailWarmupStatuschannelName,
      event_name: CONSTANTS.EmailAccountWarmupStatusChannelName,

      on_event: (response) => {
        this.updateEmailAccountStatus(response.event_data);
      },
    });

    // Add event listener for clicking outside the dropdown
    document.addEventListener("mousedown", this.handleClickOutside);

    if (query.code && query.state) {
      this.setState({ codeUrl: query.code, stateUrl: query.state }, () => {
        const stateUrl = this.state.stateUrl as string;
        const tempArr = stateUrl.split("___");
        console.log("state: ", stateUrl, tempArr);
        const tidString = tempArr[1];
        const campaignIdString = tempArr[2];

        const campaign_basic_setup = tempArr[6] === "true" ? true : false;

        const is_inbox = tempArr[8] === "true" ? true : false;

        const goto_quickstart = tempArr[9] === "true" ? true : false;

        const tid = parseInt(tidString);
        const campaignId = parseInt(campaignIdString);

        this.props.history.push({
          pathname: "/dashboard/account_settings/email_accounts",
          search: queryString.stringify({
            tid: tid,
          }),
        });

        const code =
          "?code=" + this.state.codeUrl + "&state=" + this.state.stateUrl;
        authAPI.sendOAuthcode(code, false, false).then(
          (res: any) => {
            const campaignId = res.data.campaign_id;

            if (goto_quickstart && res.data.emails && res.data.emails.length) {
              const url = "/dashboard/quickstart";
              this.props.history.push({
                pathname: url,
                search: queryString.stringify({
                  tid: tid,
                  redirect: true,
                }),
              });
            } else if (
              campaignId &&
              res.data.emails &&
              res.data.emails.length
            ) {
              const id = res.data.emails[0].id;
              const email_type = res.data.email_type;
              const url = campaign_basic_setup
                ? "/dashboard/campaigns/" + campaignId + "/basic-setup"
                : "/dashboard/campaigns/" + campaignId + "/channel_setup/email";

              var queryStringParams: any = {
                tid: tid,
                redirect: true,
              };

              if (id) {
                queryStringParams["emailId"] = id;
              }

              if (email_type) {
                queryStringParams["email_type"] = email_type;
              }
              console.log("success send oauth cade 1");

              this.props.history.push({
                pathname: url,
                search: queryString.stringify(queryStringParams),
              });
              console.log("success send oauth cade 2");
            } else if (is_inbox && res.data.emails && res.data.emails.length) {
              const url = "/dashboard/inbox/team_inbox?tid=" + tid;
              this.props.history.push({
                pathname: url,
                search: queryString.stringify({
                  tid: tid,
                  redirect: true,
                }),
              });
              console.log("success send oauth cade 5");
            } else {
              console.log("success send oauth cade 3");

              this.props.history.push({
                pathname: "/dashboard/account_settings/email_accounts",
                search: queryString.stringify({
                  tid: tid,
                }),
              });
              console.log("success send oauth cade 4");
              this.setState({
                newEmailSettingId: parseInt(res.data.emails[0].id),
                newEmailAccount: res.data.emails[0].email,
                showAddTeamInboxModal: true,
              });

              this.getEmailSettingsApi();
            }
          },
          (error) => {
            if (goto_quickstart) {
              const url = "/dashboard/quickstart";
              this.props.history.push({
                pathname: url,
                search: queryString.stringify({
                  tid: tid,
                  redirect: true,
                }),
              });
            } else if (campaignId) {
              const url = "/dashboard/campaigns/" + campaignId + "/prospects";
              this.props.history.push({
                pathname: url,
                search: queryString.stringify({
                  tid: tid,
                  redirect: true,
                }),
              });
            } else if (is_inbox) {
              const url = "/dashboard/inbox/team_inbox?tid=" + tid;
              this.props.history.push({
                pathname: url,
                search: queryString.stringify({
                  tid: tid,
                  redirect: true,
                }),
              });
            } else {
              this.props.history.push({
                pathname: "/dashboard/account_settings/email_accounts",
                search: queryString.stringify({
                  tid: tid,
                }),
              });
              this.getEmailSettingsApi();
            }
            return error;
          }
        );
      });
    } else {
      this.getEmailSettingsApi();
    }

    const teamId = this.props.logInStore.getCurrentTeamId;
    const channelName = getPusherChannelNameForEmailHealthCheck(teamId);

    pusherSubscribe<settingsApi.EmailHealthCheckRecordWithEmailId>({
      channel_name: channelName,
      event_name: "email_health_check.completed",
      on_event: (data) => {
        this.setState((prevState) => {
          const newMap = new Map(prevState.emailHealthCheckMap);
          const hcr = data.event_data;

          newMap.set(hcr.email_setting_id, {
            spf_auth_status: hcr.spf_auth_status,
            dkim_auth_status: hcr.dkim_auth_status,
            dmarc_auth_status: hcr.dmarc_auth_status,
            status: hcr.status,
          });

          return { ...prevState, emailHealthCheckMap: newMap };
        });
      },
    });
  }

  ownerFormatter(ownerName: string) {
    return <SRAvatar name={ownerName} />;
  }

  updateEmailAccountStatus(
    emailAccountStatus: settingsApi.EmailAccountWarmupStatus
  ) {
    let emailAccountWarmupStatusCopy = new Map(
      this.state.emailAccountWarmupStatus
    );

    emailAccountWarmupStatusCopy.set(
      emailAccountStatus.email,
      emailAccountStatus
    );

    this.setState({
      emailAccountWarmupStatus: emailAccountWarmupStatusCopy,
    });
  }

  reconnectEmailAccount(email: Settings.IEmailData) {
    this.setState((prev) => ({
      ...prev,
      zapmailAutoReconnectLoading: [
        ...prev.zapmailAutoReconnectLoading,
        Number(email.id),
      ],
    }));

    emailInfra
      .reconnectEmailAccount(email.email)
      .then((_) => {
        this.getEmailSettingsApi();
      })
      .catch((err) => {
        console.log("error reconnecting email account", err);
      })
      .finally(() => {
        this.setState((prev) => ({
          ...prev,
          zapmailAutoReconnectLoading: prev.zapmailAutoReconnectLoading.filter(
            (id) => id !== Number(email.id)
          ),
        }));
      });
  }

  renderEmailSettingRow(email: Settings.IEmailData) {
    const logInStore = this.props.logInStore;
    const permissions = logInStore.getTeamRolePermissions.permissions;
    const isOwner = email.owner_id === logInStore.getAccountInfo.internal_id;

    const emailHeathCheckData = this.state.emailHealthCheckMap.get(+email.id);

    const emailWarmupStatus = this.state.emailAccountWarmupStatus.get(
      email.email
    );

    const canAutoStartWarmup = email.can_auto_start_warmup;

    const healthCheckLoading = !!this.state.emailHeachCheckLoading.find(
      (id) => id === +email.id
    );

    const zapmailAutoReconnectLoading =
      !!this.state.zapmailAutoReconnectLoading.find((id) => id === +email.id);

    const isSelected = this.state.selectedEmailIds.includes(+email.id);

    return _.map(this.getTableColumns(), (column, index) => {
      if (column.key === "checkbox") {
        return (
          <div key={column.key}>
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) =>
                this.handleEmailSelection(+email.id, e.target.checked)
              }
              className="w-4 h-4 text-sr-default-blue bg-gray-100 border-gray-300 rounded focus:ring-sr-default-blue"
            />
          </div>
        );
      } else if (column.key === "email_and_name") {
        return (
          <div>
            <div className="flex item-center">
              {!!email.error && (
                <div className="mr-[5px] flex items-center">
                  <SRTooltip text={email.error!} direction="top-left">
                    <SrIconAlert className="w-5 text-red-600" />
                  </SRTooltip>
                </div>
              )}
              {email.campaign_use_status_for_email_setting ===
              "has_running_campaign" ? (
                <SRPopover
                  triggerElement={
                    <div className="bg-sr-default-green p-[3px] rounded-full mr-[5px]"></div>
                  }
                >
                  <div className="break-normal">
                    This email is used as sender email in a running campaign
                  </div>
                </SRPopover>
              ) : email.campaign_use_status_for_email_setting ===
                "no_running_campaign" ? (
                <SRPopover
                  triggerElement={
                    <div className="bg-sr-default-grey p-[3px] rounded-full mr-[5px]"></div>
                  }
                >
                  <div className="break-normal">
                    This email is used as sender email in an inactive campaign
                  </div>
                </SRPopover>
              ) : (
                <div className="mx-[5px]" />
              )}
              <div className="flex flex-col">
                <a
                  onClick={this.openOptionsModal.bind(this, email)}
                  className="text-sr-text-grey hover:text-sr-default-blue cursor-pointer truncate max-w-[200px]"
                >
                  {email.email}
                </a>
                <div>{email.first_name + " " + email.last_name}</div>
                {email.tag && <SRLabel3 text={email.tag} color="blue" />}
              </div>
            </div>
          </div>
        );
      } else if (column.key === "owner_name") {
        return this.ownerFormatter(email.owner_name || "");
      } else if (column.key === "can_send_receive") {
        return (
          <div className="flex flex-row space-x-1">
            {email.can_send ? (
              <div className="rounded-full self-center w-5 h-5 flex items-center">
                <SrIconCircleTickFilled className="w-[20px] h-[20px] text-white" />
              </div>
            ) : (
              <div className="rounded-full self-center w-5 h-5 flex items-center">
                <SrIconClose className="w-[20px] h-[20px] text-[#C81E1E]" />
              </div>
            )}
            <div>/</div>
            {email.can_receive ? (
              <div className="rounded-full self-center w-5 h-5 flex items-center">
                <SrIconCircleTickFilled className="w-[20px] h-[20px] text-white" />
              </div>
            ) : (
              <div className="rounded-full self-center w-5 h-5 flex items-center">
                <SrIconClose className="w-[20px] h-[20px] text-[#C81E1E]" />
              </div>
            )}
          </div>
        );
      } else if (column.key === "first_name") {
        return <div key={column.key}>{email.first_name}</div>;
      } else if (column.key === "last_name") {
        return <div key={column.key}>{email.last_name}</div>;
      } else if (column.key === "service_provider") {
        return (
          <div key={column.key}>
            {email.service_provider === "gmailapi"
              ? "gmail"
              : email.service_provider}
          </div>
        );
      } else if (column.key === "quota_per_day") {
        return (
          <div key={column.key} className="flex justify-center">
            {email.quota_per_day}
          </div>
        );
      } else if (column.key === "rms_ip") {
        return (
          <div key={column.key} className="flex justify-center">
            {email.rms_ip}
          </div>
        );
      } else if (column.key === "ehc") {
        return (
          <div className="w-[180px]">
            {!!email.error && email.can_auto_reconnect_via_oauth ? (
              <div>
                <SRButtonOutline
                  icon="sr_icon_refresh"
                  iconPosition="left"
                  text="Reconnect"
                  className="rounded-full px-[16px]"
                  loading={zapmailAutoReconnectLoading}
                  disable={
                    email.service_provider !== "gmailapi" &&
                    email.service_provider !== "office365"
                  }
                  onClick={() => this.reconnectEmailAccount(email)}
                />
              </div>
            ) : (
              <div>
                <div>
                  {!!emailHeathCheckData ? (
                    <div>
                      {emailHeathCheckData.status === "pending" ||
                      emailHeathCheckData.status === "queued" ? (
                        <div>
                          <span className="inline-flex items-center">
                            <div className="bg-sr-gray-80 p-[3px] rounded-full mr-[5px] w-[1px] h-[1px] " />
                            Health check in progress
                          </span>
                        </div>
                      ) : (
                        <div className="inline-flex justify-between mt-[5px] w-[180px]">
                          <span className="flex items-center">
                            {getEmailHealthCheckAuthStatusIcon({
                              authStatus: emailHeathCheckData.dmarc_auth_status,
                            })}
                            DMARC
                          </span>
                          <span className="flex items-center">
                            {getEmailHealthCheckAuthStatusIcon({
                              authStatus: emailHeathCheckData.dkim_auth_status,
                            })}
                            DKIM
                          </span>
                          <span className="flex items-center">
                            {getEmailHealthCheckAuthStatusIcon({
                              authStatus: emailHeathCheckData.spf_auth_status,
                            })}
                            SPF
                          </span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div>
                      <span className="inline-flex items-center">
                        <div className="bg-sr-gray-80 p-[3px] rounded-full mr-[5px] w-[1px] h-[1px] " />
                        Health check pending
                      </span>
                    </div>
                  )}
                </div>
                <div className="mt-[4px]">
                  <SRButtonOutline
                    icon="sr_icon_health_check"
                    iconPosition="left"
                    text={!emailHeathCheckData ? "Run checks" : "Re-run checks"}
                    className="rounded-full px-[16px]"
                    disable={
                      emailHeathCheckData?.status === "pending" ||
                      emailHeathCheckData?.status === "queued"
                    }
                    loading={healthCheckLoading}
                    loadingText="Running checks&nbsp;"
                    onClick={() => this.checkEmailHealth(+email.id)}
                  />
                </div>
              </div>
            )}
          </div>
        );
      } else if (column.key === "actions") {
        return (
          <div className="flex" key={column.key}>
            <>
              <SRButtonText
                icon="sr_icon_edit"
                onClick={this.openOptionsModal.bind(this, email)}
                className="hover:!text-sr-default-blue hover:!bg-transparent"
                iconClassName="!h-[20px] !w-[20px]"
                title="Edit email settings"
                isPrimary={true}
              />
              <SRButtonText
                icon="sr_icon_delete"
                onClick={this.openDeleteConfirmModal.bind(
                  this,
                  email.id,
                  email.email
                )}
                disable={!checkPermission(permissions.delete_channels, isOwner)}
                className="hover:!text-sr-default-red hover:!bg-transparent"
                iconClassName="!h-[20px] !w-[20px]"
                title="Delete email account"
                isNegative={true}
              />
            </>
          </div>
        );
      } else if (column.key === "auto_connect") {
        return (
          <div className="w-[180px]">
            {canAutoStartWarmup ? (
              <EmailAccountWarmupStatusCell
                status={emailWarmupStatus}
                emailSettingUuid={email.uuid}
                initialLoading={this.state.emailAccountWarmupStatusLoading}
                updateEmailAccountStatus={this.updateEmailAccountStatus}
              />
            ) : (
              <div>
                <span className="inline-flex items-center">
                  Cannot auto connect
                </span>
              </div>
            )}
          </div>
        );
      } else {
        return;
      }
    }).map((cell) => ({ cell: cell }));
  }

  // Handle email selection via checkbox
  handleEmailSelection(emailId: number, isChecked: boolean) {
    if (isChecked) {
      this.setState((prevState) => ({
        selectedEmailIds: [...prevState.selectedEmailIds, emailId],
      }));
    } else {
      this.setState((prevState) => ({
        selectedEmailIds: prevState.selectedEmailIds.filter(
          (id) => id !== emailId
        ),
      }));
    }
  }

  // Open bulk update modal with specific type
  openBulkUpdateModal(type: "quota" | "delay" | "owner" | "delete" | "tag") {
    if (type === "owner") {
      this.openOwnerChangeModal();
      return; // Return early to ensure bulkUpdateType is never set to 'owner'
    } else if (type === "delete") {
      this.openBulkDeleteModal();
      return; // Return early to ensure bulkUpdateType is never set to 'delete'
    } else if (type === "tag") {
      this.openBulkAddTagModal();
      return; // Return early to ensure bulkUpdateType is never set to 'tag'
    } else {
      this.setState({
        isBulkUpdateModalOpen: true,
        bulkUpdateType: type,
      });
    }
  }

  // Close bulk update modal
  closeBulkUpdateModal() {
    this.setState({
      isBulkUpdateModalOpen: false,
      bulkUpdateType: null,
    });
  }

  // Submit bulk quota update
  submitBulkQuotaUpdate(quotaPerDay: number) {
    this.setState({ isSubmittingBulkUpdate: true });

    const data = {
      es_ids: this.state.selectedEmailIds,
      quota_per_day: quotaPerDay,
    };

    settingsApi
      .updateBulkEmailSettings(data)
      .then((response) => {
        // Update the email data in the state with the updated emails from the response
        const updatedEmails = response.data.emails;
        let emailData = this.state.emailData || [];

        updatedEmails.forEach((updatedEmail) => {
          const index = emailData.findIndex(
            (email) => email.id === updatedEmail.id
          );
          if (index !== -1) {
            emailData[index] = updatedEmail;
          }
        });

        this.setState({
          selectedEmailIds: [],
          emailData,
          isSubmittingBulkUpdate: false,
          filtered_email_data: this.state.searchInput
            ? emailData.filter((p) =>
                p.email.includes(this.state.searchInput!.trim())
              )
            : undefined,
        });
        this.closeBulkUpdateModal();
      })
      .catch(() => {
        this.setState({ isSubmittingBulkUpdate: false });
      });
  }

  // Submit bulk delay update
  submitBulkDelayUpdate(minDelay: number, maxDelay: number) {
    this.setState({ isSubmittingBulkUpdate: true });

    const data = {
      es_ids: this.state.selectedEmailIds,
      min_delay_seconds: minDelay,
      max_delay_seconds: maxDelay,
    };

    settingsApi
      .updateBulkEmailSettings(data)
      .then((response) => {
        // Update the email data in the state with the updated emails from the response
        const updatedEmails = response.data.emails;
        let emailData = this.state.emailData || [];

        updatedEmails.forEach((updatedEmail) => {
          const index = emailData.findIndex(
            (email) => email.id === updatedEmail.id
          );
          if (index !== -1) {
            emailData[index] = updatedEmail;
          }
        });

        this.setState({
          selectedEmailIds: [],
          emailData,
          isSubmittingBulkUpdate: false,
          filtered_email_data: this.state.searchInput
            ? emailData.filter((p) =>
                p.email.includes(this.state.searchInput!.trim())
              )
            : undefined,
        });
        this.closeBulkUpdateModal();
      })
      .catch(() => {
        this.setState({ isSubmittingBulkUpdate: false });
      });
  }

  // Handle clicks outside the dropdown
  handleClickOutside = (event: MouseEvent) => {
    const dropdown = document.getElementById("bulk-action-dropdown");
    const buttonEl = document.querySelector(".bulk-action-button");

    if (
      dropdown &&
      buttonEl &&
      !dropdown.contains(event.target as Node) &&
      !buttonEl.contains(event.target as Node)
    ) {
      dropdown.classList.add("hidden");
    }
  };

  // Add these functions for handling owner change modal
  openOwnerChangeModal() {
    this.setState({ isOwnerChangeModalOpen: true });
  }

  closeOwnerChangeModal() {
    this.setState({ isOwnerChangeModalOpen: false });
  }

  // Open bulk delete modal
  openBulkDeleteModal() {
    this.setState({
      isBulkDeleteModalOpen: true,
    });
  }

  // Close bulk delete modal
  closeBulkDeleteModal() {
    this.setState({
      isBulkDeleteModalOpen: false,
    });
  }

  // Submit bulk delete
  submitBulkDelete() {
    this.setState({ isSubmittingBulkDelete: true });

    settingsApi
      .bulkDeleteEmailAccounts(this.state.selectedEmailIds)
      .then(() => {
        this.setState({
          isSubmittingBulkDelete: false,
          isBulkDeleteModalOpen: false,
          selectedEmailIds: [],
        });
        this.getEmailSettingsApi();
      })
      .catch(() => {
        this.setState({
          isSubmittingBulkDelete: false,
          isBulkDeleteModalOpen: false,
        });
      });
  }

  // Open bulk add tag modal
  openBulkAddTagModal() {
    this.setState({
      isBulkTagModalOpen: true,
    });
  }

  // Close bulk add tag modal
  closeBulkTagModal() {
    this.setState({
      isBulkTagModalOpen: false,
    });
  }

  // Submit bulk add tag
  submitBulkAddTag(tagsSelected: string[], emailIds: number[]) {
    // Only take the first tag since one email can have only one tag
    const tag = tagsSelected[0];

    if (!tag) {
      return;
    }

    this.setState({ isSubmittingBulkTag: true });

    const data = {
      es_ids: emailIds,
      tag: tag,
    };

    settingsApi
      .addTagToEmails(data)
      .then((response) => {
        // Update the email data in the state with the updated emails from the response
        const updatedEmails = response.data.emails;
        let emailData = this.state.emailData || [];

        updatedEmails.forEach((updatedEmail) => {
          const index = emailData.findIndex(
            (email) => email.id === updatedEmail.id
          );
          if (index !== -1) {
            emailData[index] = updatedEmail;
          }
        });

        this.setState({
          selectedEmailIds: [],
          emailData,
          isSubmittingBulkTag: false,
          isBulkTagModalOpen: false,
          filtered_email_data: this.state.searchInput
            ? emailData.filter((p) =>
                p.email.includes(this.state.searchInput!.trim())
              )
            : undefined,
        });
      })
      .catch(() => {
        this.setState({
          isSubmittingBulkTag: false,
          isBulkTagModalOpen: false,
        });
      });
  }

  render() {
    const s = this.state;
    const emailData = s.emailData || [];
    const filtered_email_data = s.filtered_email_data || emailData;
    const currentEmailData = s.currentEmailData || this.getEmptyEmailData();
    const action = s.action || "Add";
    // const showMessage = this.state.showMessage;
    const selectedEmailsCount = this.state.selectedEmailIds.length;

    const logInStore = this.props.logInStore;
    const permissions = logInStore.getTeamRolePermissions.permissions;
    const isOwner =
      currentEmailData.owner_id === logInStore.getAccountInfo.internal_id;

    const canViewEmailAccountsPage = checkPermission(permissions.view_channels);
    const canEditEmailAccountsPage = checkPermission(permissions.edit_channels);

    const is_team_inbox_enabled =
      logInStore.accountInfo.org.org_metadata.is_team_inbox_enabled ?? false;

    // Get selected email addresses for bulk delete modal
    const selectedEmails = this.state.selectedEmailIds
      .map((id) => {
        const email = emailData.find((e) => +e.id === id);
        return email ? email.email : "";
      })
      .filter(Boolean);

    return (
      <div className="p-2">
        <div className="flex justify-between">
          <div className="sr-h3">Email Accounts</div>

          <div className="action-button">
            {canEditEmailAccountsPage &&
              emailData.length !== 0 &&
              !s.isLoading && (
                <div className="flex gap-3">
                  <SRButtonOutline
                    text="Upload emails (for smtp/imap)"
                    onClick={this.openEmailUploadModal}
                  />

                  <SRButtonFilled
                    text="Add Email Account"
                    type="button"
                    icon="sr_icon_add"
                    iconPosition="left"
                    className="m-auto px-4 py-2"
                    isPrimary={true}
                    onClick={this.selectEmailProvider}
                  />
                </div>
              )}
          </div>
        </div>
        <div className="py-[2em] mb-[2em]">
          {s.isLoading && <Spinner spinnerTitle="loading .." />}

          {canEditEmailAccountsPage &&
            canViewEmailAccountsPage &&
            !s.isLoading &&
            emailData.length === 0 && (
              <div className="w-full flex flex-col items-center justify-center gap-3 mt-8">
                <img
                  src={CONSTANTS.CDN_URL + "/assets/jan25/empty_email_img.png"}
                  className="w-[140px] h-[92px]"
                />
                <div className="sr-h3">Add Your First Email Account</div>
                <div className="sr-p text-sr-grey-90">
                  Start by adding an email account to send your first campaign
                </div>
                <SRButtonFilled
                  isPrimary
                  text="Add email account"
                  icon="sr_icon_add"
                  className="!px-12 !py-3 !text-[16px] !font-sourcesanspro !font-semibold"
                  onClick={this.selectEmailProvider}
                />
                <SRButtonOutline
                  text="Upload emails (for smtp/imap)"
                  textBold
                  className="!border !border-sr-gray-30 !px-4 !py-3 !text-[16px] !font-sourcesanspro !text-sr-grey-100"
                  onClick={this.openEmailUploadModal}
                />
              </div>
            )}

          {!checkPermission(permissions.edit_channels) &&
            !s.isLoading &&
            emailData.length === 0 && (
              <div className="ui grid margin top ten percent empty campaign-list">
                <div className="seven wide column centered">
                  <h2 className="ui center aligned icon header">
                    <i className="settings icon"></i>
                    <div className="content">
                      No email accounts to show
                      <div className="sub header">
                        No email accounts have yet been linked by the account
                        owner
                      </div>
                    </div>
                  </h2>
                </div>
              </div>
            )}

          {!s.isLoading &&
            emailData.length !== 0 &&
            canViewEmailAccountsPage && (
              <div>
                <div className="flex justify-between my-4">
                  <SRInput
                    type="text"
                    handleChange={(ev) =>
                      !!ev.target.value.trim()
                        ? this.setState({
                            searchInput: ev.target.value,
                            filtered_email_data: emailData.filter((p) =>
                              p.email.includes(ev.target.value.trim())
                            ),
                          })
                        : this.setState({
                            searchInput: undefined,
                            filtered_email_data: emailData,
                          })
                    }
                    className="sr-h7 mb-0 p-4 !w-[400px]"
                    name="search_prospect"
                    iconLeft="sr_icon_search"
                    placeholder="Search by Email"
                  />

                  {/* {selectedEmailsCount > 0 && ( */}
                  <div className="relative inline-block text-left">
                    <SRButtonFilled
                      text="Bulk Actions"
                      type="button"
                      icon="sr_icon_chevron_down"
                      disable={selectedEmailsCount === 0}
                      iconPosition="right"
                      className="m-auto px-4 py-2 bulk-action-button"
                      isPrimary={true}
                      onClick={() => {
                        const dropdown = document.getElementById(
                          "bulk-action-dropdown"
                        );
                        if (dropdown) {
                          dropdown.classList.toggle("hidden");
                        }
                      }}
                    />
                    <div
                      id="bulk-action-dropdown"
                      className="hidden absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5"
                    >
                      <div className="py-1">
                        <button
                          onClick={() => {
                            document
                              .getElementById("bulk-action-dropdown")
                              ?.classList.add("hidden");
                            this.openBulkUpdateModal("quota");
                          }}
                          className="text-gray-700 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                        >
                          Update Daily Limit
                        </button>
                        <button
                          onClick={() => {
                            document
                              .getElementById("bulk-action-dropdown")
                              ?.classList.add("hidden");
                            this.openBulkUpdateModal("delay");
                          }}
                          className="text-gray-700 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                        >
                          Update Delay
                        </button>
                        <button
                          onClick={() => {
                            document
                              .getElementById("bulk-action-dropdown")
                              ?.classList.add("hidden");
                            this.openBulkUpdateModal("owner");
                          }}
                          className="text-gray-700 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                        >
                          Update Owner
                        </button>
                        <button
                          onClick={() => {
                            document
                              .getElementById("bulk-action-dropdown")
                              ?.classList.add("hidden");
                            this.openBulkUpdateModal("tag");
                          }}
                          className="text-gray-700 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                        >
                          Add Tag
                        </button>
                        <button
                          onClick={() => {
                            document
                              .getElementById("bulk-action-dropdown")
                              ?.classList.add("hidden");
                            this.openBulkUpdateModal("delete");
                          }}
                          className="text-gray-700 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                        >
                          Delete Email Accounts
                        </button>
                      </div>
                    </div>
                  </div>
                  {/* )} */}
                </div>
                {filtered_email_data.length !== 0 ? (
                  <div className="email-accounts-list-table">
                    <SRTable
                      columns={_.map(
                        this.getTableColumns(),
                        (column, index) => {
                          return { cell: column.name, info: column.popup };
                        }
                      )}
                      rows={_.map(filtered_email_data, (email, index) => {
                        return {
                          cells: this.renderEmailSettingRow(email),
                          key: email.id,
                        };
                      })}
                    />
                  </div>
                ) : (
                  <div className="flex flex-col my-[16px]">
                    <div className="sr-h5 !text-sr-gray-80 ">
                      We could not find any emails matching your search for "
                      {s.searchInput}"
                    </div>
                  </div>
                )}
              </div>
            )}

          {!s.isLoading &&
            emailData.length !== 0 &&
            !canViewEmailAccountsPage && (
              <div className="ui grid margin top ten percent empty campaign-list">
                <div className="seven wide column centered">
                  <h2 className="ui center aligned icon header">
                    <i className="settings icon"></i>
                    <div className="content">
                      Not accessible
                      <div className="sub header">
                        You are not authorised to access this information
                      </div>
                    </div>
                  </h2>
                </div>
              </div>
            )}

          {this.state.isEmailSettingsModalOpen && (
            <EmailSettingsModal
              onSubmit={this.onSubmitSmtpImap}
              helpful_error_details={this.state.helpful_error_details}
              emailData={currentEmailData}
              onClose={this.onCloseSmtpImap}
              isSubmitting={this.state.isEmailSettingsModalLoading || false}
              onHelpfulMessageDismiss={this.onHelpfulMessageDimiss}
              action={action}
              step_index={this.state.step_index as 1 | 2}
              onNext={
                action === "Add" ? this.testOnAddSmtpImap.bind(this) : () => {}
              }
              onBack={
                action === "Add"
                  ? () => this.setState({ step_index: 1 })
                  : () => {}
              }
              service_provider={this.state.selectedProviderKey}
              updatekeyToReRender={this.updatekeyToReRender}
              canEdit={checkPermission(permissions.edit_channels, isOwner)}
              canReconnect={checkPermission(permissions.edit_channels, isOwner)}
              accountId={
                (this.props.logInStore || ({} as LogIn.ILogInStore))
                  .getAccountInfo.internal_id
              }
            />
          )}

          {this.state.showEmailProviderList && (
            <EmailProviderListModal
              onProviderSelect={this.onProviderSelect}
              onClose={this.closeEmailProviderListModal}
              getOAuthRedirectUrlErrorCode={
                this.state.getOAuthRedirectUrlErrorCode
              }
              isEmailSettingsModalGsuiteSubmitLoading={
                this.state.isEmailSettingsModalGsuiteSubmitLoading
              }
            />
          )}

          {this.state.openEmailUploadModal && (
            <UploadEmailModal
              onClose={this.closeEmailUploadModal}
              onFinish={this.closeEmailUploadModal}
            />
          )}

          {this.state.showDeleteConfirmModal && (
            <GenericDeleteConfirmModalV2
              onConfirm={this.deleteEmailAccount}
              onCancel={this.closeDeleteConfirmModal}
              heading="Are you sure you want to delete this email account?"
              modalLoading={this.state.deletingEmail || false}
              buttonText="Delete Email Account"
            >
              <div className="pt-5 pb-4">
                <h4 className="mb-2 sr-h4">
                  <strong>{this.state.toDeleteEmail}</strong>
                </h4>
                <p className="mb-1">
                  Data related to this email account will be deleted
                  permanently.
                </p>
                <p>
                  Any running campaigns sending-from / receiving-replies-to this
                  email will be paused.{" "}
                </p>
              </div>
            </GenericDeleteConfirmModalV2>
          )}

          {is_team_inbox_enabled && this.state.showAddTeamInboxModal && (
            <AddTeamInboxTWModal
              onClose={this.closeAddTeamInboxModal}
              newEmailAccount={this.state.newEmailAccount}
              newEmailSettingId={this.state.newEmailSettingId}
            />
          )}

          {this.state.isEmailHealthCheckModalOpen && currentEmailData && (
            <EmailAccountHealthCheckModal
              email={currentEmailData}
              onClose={() => {
                this.setState({
                  isEmailHealthCheckModalOpen: false,
                  currentEmailData: undefined,
                });
              }}
            />
          )}

          {this.state.isEditEmailSettingsOpen && (
            <EditSettingsModal
              onBasicDetailsSubmit={this.testUpdateSmtpImap.bind(
                this,
                currentEmailData.id
              )}
              onEmailSettingsSubmit={this.updateEmailSettings.bind(
                this,
                currentEmailData.id
              )}
              onEditEmailSignatureSubmit={this.onSubmitEmailSignature}
              onOpenDKIMSignature={this.openDKIMSignatureModal}
              onChangeOwner={this.onChangeOwner}
              onCustomDomainTrackingModalSubmit={
                this.onCustomDomainTrackingModalSubmit
              }
              emailData={currentEmailData || this.getEmptyEmailData()}
              onClose={this.onCloseEditEmailSettingsModal.bind(this)}
              isSubmitting={
                this.state.isEmailSettingsModalLoading ||
                this.state.isEmailSignatureModalLoading ||
                this.state.isOwnerChangeModalLoading ||
                false
              }
              action="Edit"
              onVerifyDKIMSignature={this.onDKIMSignatureRecordModalSubmit}
              onGenerateDKIM={this.onGenerateDKIMSignatureRecord}
              dkimData={this.state.slectedEmailAccountDkimData}
              DKIMVerifyRes={this.state.isDKIMSignatureModalVerifyDKIMResponse}
              isDKIMVerified={this.state.isDKIMSignatureVerified}
              isDKIMSignatureModalLoading={
                this.state.isDKIMSignatureModalLoading || false
              }
              step_index={this.state.step_index}
              service_provider={this.state.selectedProviderKey}
              updatekeyToReRender={this.updatekeyToReRender}
              canEdit={checkPermission(permissions.edit_channels, isOwner)}
              canReconnect={checkPermission(permissions.edit_channels, isOwner)}
              accountId={
                (this.props.logInStore || ({} as LogIn.ILogInStore))
                  .getAccountInfo.internal_id
              }
            />
          )}

          {/* Bulk Update Modals */}
          {this.state.isBulkUpdateModalOpen &&
            this.state.bulkUpdateType === "quota" && (
              <SrModal
                title="Update Daily Limit"
                size="small"
                onClose={this.closeBulkUpdateModal}
                content={
                  <div className="p-4">
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Daily Email Quota
                      </label>
                      <input
                        type="number"
                        id="quota_per_day"
                        name="quota_per_day"
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        placeholder="Enter daily limit"
                        // defaultValue={this.props.logInStore?.getDefaultEmailQuota().toString()}
                        min="1"
                      />
                    </div>
                    <div className="flex justify-end">
                      <SRButtonOutline
                        text="Cancel"
                        onClick={this.closeBulkUpdateModal}
                        width="default"
                        className="mr-2"
                      />
                      <SRButtonFilled
                        text="Update"
                        onClick={() => {
                          const input = document.getElementById(
                            "quota_per_day"
                          ) as HTMLInputElement;
                          const value = parseInt(input.value);
                          if (value > 0) {
                            this.submitBulkQuotaUpdate(value);
                          }
                        }}
                        width="default"
                        loading={this.state.isSubmittingBulkUpdate}
                        isPrimary={true}
                      />
                    </div>
                  </div>
                }
              />
            )}

          {this.state.isBulkUpdateModalOpen &&
            this.state.bulkUpdateType === "delay" && (
              <SrModal
                title="Update Delay Settings"
                size="small"
                onClose={this.closeBulkUpdateModal}
                content={
                  <div className="p-4">
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Minimum Delay (seconds)
                      </label>
                      <input
                        type="number"
                        id="min_delay"
                        name="min_delay"
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        placeholder="Enter minimum delay"
                        // defaultValue={this.props.logInStore?.getDefaultLowerLimitForEmailDelay().toString()}
                        min="1"
                      />
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Maximum Delay (seconds)
                      </label>
                      <input
                        type="number"
                        id="max_delay"
                        name="max_delay"
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        placeholder="Enter maximum delay"
                        // defaultValue={this.props.logInStore?.getDefaultUpperLimitForEmailDelay().toString()}
                        min="1"
                      />
                    </div>
                    <div className="flex justify-end">
                      <SRButtonOutline
                        text="Cancel"
                        onClick={this.closeBulkUpdateModal}
                        width="default"
                        className="mr-2"
                      />
                      <SRButtonFilled
                        text="Update"
                        onClick={() => {
                          const minInput = document.getElementById(
                            "min_delay"
                          ) as HTMLInputElement;
                          const maxInput = document.getElementById(
                            "max_delay"
                          ) as HTMLInputElement;
                          const minValue = parseInt(minInput.value);
                          const maxValue = parseInt(maxInput.value);
                          // if (minValue > 0 && maxValue > 0 && maxValue >= minValue) {
                          this.submitBulkDelayUpdate(minValue, maxValue);
                          // }
                        }}
                        width="default"
                        loading={this.state.isSubmittingBulkUpdate}
                        isPrimary={true}
                      />
                    </div>
                  </div>
                }
              />
            )}

          {/* EmailOwnerChangeModal for bulk updates */}
          {this.state.isOwnerChangeModalOpen && (
            <SrModal
              title="Change Email Owner"
              size="small"
              onClose={this.closeOwnerChangeModal}
              content={
                <EmailOwnerChangeModal
                  emailAccountId={0} // Not used for bulk operations
                  emailAccount={`${this.state.selectedEmailIds.length} email accounts`}
                  onSubmit={(emailIds, ownerId) => {
                    this.onChangeOwner(this.state.selectedEmailIds, ownerId);
                  }}
                  isSubmitting={this.state.isOwnerChangeModalLoading}
                  canEditOwner={checkPermission(permissions.edit_channels)}
                  onClose={this.closeOwnerChangeModal}
                  accessMembers={
                    this.props.logInStore.getCurrentTeamObj.access_members
                  }
                  currentOwnerId={0} // Default to 0 for bulk operations
                  onNext={undefined}
                  onBack={undefined}
                />
              }
            />
          )}

          {/* Bulk Delete Modal */}
          {this.state.isBulkDeleteModalOpen && (
            <GenericDeleteConfirmModalV2
              onConfirm={this.submitBulkDelete}
              onCancel={this.closeBulkDeleteModal}
              heading="Are you sure you want to delete these email accounts?"
              modalLoading={this.state.isSubmittingBulkDelete}
              buttonText="Delete Email Accounts"
            >
              <div className="pt-5 pb-4">
                <h4 className="mb-2 sr-h4">
                  <strong>Selected emails ({selectedEmails.length}):</strong>
                </h4>
                <div className="max-h-40 overflow-y-auto mb-4">
                  {selectedEmails.map((email, index) => (
                    <div key={index} className="mb-1">
                      {email}
                    </div>
                  ))}
                </div>
                <p className="mb-1">
                  Data related to these email accounts will be deleted
                  permanently.
                </p>
                <p>
                  Any running campaigns sending-from / receiving-replies-to
                  these emails will be paused.
                </p>
              </div>
            </GenericDeleteConfirmModalV2>
          )}

          {/* Bulk Add Tag Modal */}
          {this.state.isBulkTagModalOpen && (
            <AddEmailTagsModal
              onSubmit={this.submitBulkAddTag}
              onClose={this.closeBulkTagModal}
              isSubmitting={this.state.isSubmittingBulkTag}
              emailIds={this.state.selectedEmailIds}
              selectedEmailsCount={this.state.selectedEmailIds.length}
            />
          )}
        </div>
      </div>
    );
  }
}
export const EmailAccountsSettingsPage = withRouter(
  inject(
    "logInStore",
    "alertStore",
    "teamStore"
  )(observer(EmailAccountsSettingsPageComponent))
);
