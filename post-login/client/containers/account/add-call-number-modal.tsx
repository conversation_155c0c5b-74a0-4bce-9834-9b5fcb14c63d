import { inject, observer } from "mobx-react";
import * as React from "react";
import * as _ from 'lodash';

// import { crispShowChatBox } from "../../utils/crisp";

import { withRouter, RouteComponentProps } from 'react-router-dom';
import * as settingsAPI from '../../api/settings';
// import { PhoneNumberBasicInputs } from "./PhoneNumberBasicInputs";
// import { SmsLimitsInput } from "./SmsLimitsInput";
import { SRButtonFilled, SrModal, SRInput, SRIconPhone, SRIconEnhance } from "@sr/design-component";
import { ISRDropdownOption, SRSearchDropdown } from "@sr/design-component";
import { LogIn, Settings } from '@sr/shared-product-components';
import { CONSTANTS } from "../../data/constants";
import { PlanBasedModalWrapper } from "../billing/client/components/billing_v2/plan-based-modal-wrapper";
import { CreditCard } from "lucide-react";

interface IAddPhoneNumberModalProps extends RouteComponentProps<any> {
  logInStore?: LogIn.ILogInStore;

  // FIXME : We are passing this object as {} in some places. Instead we should make this props as optional.
  selectedPhoneNumber: Settings.ICallSettingsData;
  isEditing: boolean;
  onClose: () => void;
}

interface IAddPhoneNumberModalState {

  isSubmitting: boolean;
  // phone_number: string;
  first_name: string;
  last_name: string;
  country_options: ISRDropdownOption[];
  callerid_options: ISRDropdownOption[];
  selected_caller_id?: string;
  selected_country_code: string;
  price_of_number: number;
  currency: string;
  show_pricing: boolean;
  show_error_message: boolean;
  enable_forwarding: boolean;
  enable_call_recording: boolean;
  forwarding_number?: string;
  call_limit: number;
  show_first_name_empty_error: boolean;
  show_last_name_empty_error: boolean;
  show_subscription_required: boolean;
  show_upgrade_modal: boolean;
  // ownerId: number;
  // step: number;

  // isFirstNameEmpty: boolean;
  // isLastNameEmpty: boolean;
  // isPhoneNumberEmpty: boolean;
  // isOwnerEmpty: boolean;

  // smsLimit: number;

  isLoading: boolean;
}

// const defaultLimitValue = 50

class AddCallNumberModalComponent extends React.Component<IAddPhoneNumberModalProps, IAddPhoneNumberModalState> {

  constructor(props: IAddPhoneNumberModalProps) {
    super(props);

    const firstName = this.props.logInStore?.accountInfo.first_name
    const lastName = this.props.logInStore?.accountInfo.last_name

    this.state = {
      isSubmitting: false,
      // phone_number: this.props.selectedPhoneNumber.phone_number || "",
      first_name: this.props.selectedPhoneNumber.first_name || firstName || '',
      last_name: this.props.selectedPhoneNumber.last_name || lastName || '',
      callerid_options: [] as ISRDropdownOption[],
      selected_caller_id: this.props.selectedPhoneNumber.caller_id,
      country_options: [] as ISRDropdownOption[],
      selected_country_code: this.props.selectedPhoneNumber.country || "US",
      price_of_number: 0,
      currency:'',
      show_pricing: false,
      show_error_message: false,
      enable_forwarding: this.props.selectedPhoneNumber.enable_forwarding|| false,
      forwarding_number: this.props.selectedPhoneNumber.forward_to || undefined,
      enable_call_recording: this.props.selectedPhoneNumber.record_call == undefined ? true : this.props.selectedPhoneNumber.record_call,
      call_limit: this.props.selectedPhoneNumber.call_limit_per_day || 50,
      show_first_name_empty_error: false,
      show_last_name_empty_error: false,
      show_subscription_required: false,
      show_upgrade_modal: false,
      // ownerId: this.props.selectedPhoneNumber.owner_id || 0,
      // step: 0,

      // isFirstNameEmpty: false,
      // isLastNameEmpty: false,
      // isPhoneNumberEmpty: false,
      // isOwnerEmpty: false,

      // smsLimit: this.props.selectedPhoneNumber.sms_limit_per_day || defaultLimitValue,

      isLoading: false,
    }

    // this.onChangePhoneNumber = this.onChangePhoneNumber.bind(this);
    this.onChangeFirstName = this.onChangeFirstName.bind(this);
    this.onChangeLastName = this.onChangeLastName.bind(this);
    // this.onChangeAccountOwner = this.onChangeAccountOwner.bind(this);
    // this.onChangeSmsLimit = this.onChangeAccountOwner.bind(this);
    // this.backClick = this.backClick.bind(this);
    // this.proceedClick = this.proceedClick.bind(this);
    // this.validateInput = this.validateInput.bind(this);
    this.onCountrySelectionChange = this.onCountrySelectionChange.bind(this)
    this.getPricingAPI = this.getPricingAPI.bind(this)
    this.onChangeForwardingNumber = this.onChangeForwardingNumber.bind(this)
    this.onChangeCallLimit = this.onChangeCallLimit.bind(this)
    this.onUpdate = this.onUpdate.bind(this)
    this.buyNumber = this.buyNumber.bind(this);
    this.validateData = this.validateData.bind(this);
    this.onCalleridSelectionChange = this.onCalleridSelectionChange.bind(this)
    this.openUpgradeModal = this.openUpgradeModal.bind(this)
    this.closeUpgradeModal = this.closeUpgradeModal.bind(this)
  }

  validateData(){
    this.setState({
      show_first_name_empty_error: false,
      show_last_name_empty_error: false
    }, () => {
      const first_name = this.state.first_name.trim();
      const last_name = this.state.last_name.trim();

      if (first_name.length > 0 && last_name.length > 0) {
        if (this.props.isEditing) {
          this.props.selectedPhoneNumber.phone_number === undefined
            ? this.buyNumber() : this.onUpdate()
        } else {

          this.buyNumber()

        }
      }
      else{
        if (first_name.length === 0) {
        this.setState({ show_first_name_empty_error: true })
      }
      if (last_name.length === 0) {
        this.setState({ show_last_name_empty_error: true })
      }


    }})

  }

  buyNumber() {
    this.setState({isSubmitting: true})
    const data: Settings.IBuyNumberData = {
      first_name: this.state.first_name,
      last_name: this.state.last_name,
      country_code:this.state.selected_country_code,
      enable_call_recording: this.state.enable_call_recording,
      enable_forward: this.state.enable_forwarding,
      forward_number: this.state.forwarding_number,
      call_limit: this.state.call_limit,
      phone_uuid: this.props.selectedPhoneNumber.uuid,
      phone_type:"local" //FIXME CALL

    }
    settingsAPI.addNewNumber(data, true)
      .then((response) => {
        console.log("response data", response)
        this.setState({isSubmitting: false})
        this.props.onClose();
        window.location.reload();
      })
      .catch((err) => {
        console.log("error", err)
        if(err.message == CONSTANTS.ERROR_MESSAGE_FOR_NUMBER_NOT_AVAILABLE_FOR_PURCHAGE){
          this.setState({show_error_message: true, isSubmitting: false})
        } else {

          this.setState({
            isSubmitting: false,

          })

          console.log('debug showed error directly to user', err);
        }
      })
  }

  onUpdate(){
    this.setState({ isSubmitting: true })
    const data: Settings.IBuyNumberData = {
      first_name: this.state.first_name,
      last_name: this.state.last_name,
      country_code: this.state.selected_country_code,
      enable_call_recording: this.state.enable_call_recording,
      enable_forward: this.state.enable_forwarding,
      forward_number: this.state.forwarding_number,
      call_limit: this.state.call_limit,
      caller_id: this.state.selected_caller_id,
      phone_type: "local" //FIXME CALL

    }
    settingsAPI.updateCallAccountSettings(data, this.props.selectedPhoneNumber.uuid)
      .then((response) => {
        console.log("response data", response)
        this.setState({ isSubmitting: false })
        this.props.onClose();
        window.location.reload();
      })
      .catch((err) => {
        console.log("error", err)
        this.setState({ isSubmitting: false })
      })

  }

  // onChangePhoneNumber(e: any, data: any) {
  //   this.setState({ phone_number: data.value });
  // }

  onChangeFirstName(e: any) {
    this.setState({ first_name: e.target.value });
  }

  onChangeLastName(e: any) {
    this.setState({ last_name: e.target.value });
  }

  onChangeForwardingNumber(e: any) {
    this.setState({forwarding_number: e.target.value})
  }

  onChangeCallLimit(e: any){
    this.setState({call_limit: +e.target.value})
  }

  // onChangeAccountOwner(e: any, data: any) {
  //   this.setState({ ownerId: data.value })
  // }

  // onChangeSmsLimit(e: any, data: any) {
  //   this.setState({ smsLimit: Number(data.value) })
  // }

  // backClick() {
  //   let currentStep = this.state.step || 0;
  //   currentStep = this.state.step - 1;
  //   this.setState({ step: currentStep })
  // }

  // validateInput() {
  //   const { phone_number, first_name, last_name, ownerId } = this.state;

  //   if (first_name.trim() === '') {
  //     this.setState({ isFirstNameEmpty: true });
  //   } else if (last_name.trim() === '') {
  //     this.setState({ isLastNameEmpty: true })
  //   } else if (phone_number.trim() === '') {
  //     this.setState({ isPhoneNumberEmpty: true });
  //   } else if (ownerId === 0) {
  //     this.setState({ isOwnerEmpty: true })
  //   } else {
  //     this.proceedClick();
  //   }
  // }

  // proceedClick() {
  //   let currentStep = this.state.step || 0;
  //   currentStep = this.state.step + 1;
  //   this.setState({ step: currentStep, isPhoneNumberEmpty: false, isFirstNameEmpty: false, isLastNameEmpty: false })
  // }

  getPricingAPI(country_code: string){
    this.setState({show_pricing: false, show_error_message: false})
    console.log("country_code", country_code)
    settingsAPI.getPricing(country_code)
      .then((response) => {
        const price_object = response.data.price_details
        this.setState({show_pricing: true, price_of_number: price_object.prices.find(p=> p.phone_type=== 'local')?.current_price || 0, currency: price_object.currency})
        console.log("pricing------>", response.data)
      })
      .catch((e) => {
        this.setState({show_pricing: false,show_error_message: true})
        console.log("error----->", e)
      })

  }



  componentDidMount(): void {

    settingsAPI.getAvailableCountries()
    .then((response => {
      let options: ISRDropdownOption[]= [] as ISRDropdownOption[]


      response.data.countries.country_objects.forEach(c =>{
        let displayText = c.country_name+ `(${c.country_phone_code})`;

        // For trial users, append "- Subscription required" for countries other than US and UK
        if (response.data.countries.is_subscription_required && c.countryISO_code !== "US" && c.countryISO_code !== "GB") {
          displayText += " - Subscription required";
        }

        const option: ISRDropdownOption = {
          value: c.countryISO_code,
          displayText: displayText
        }
        options.push(option)
      })
      console.log("options-->", options)
      this.setState({country_options: options, selected_country_code:this.props.selectedPhoneNumber.country || "US"}, ()=> {
        this.getPricingAPI(this.state.selected_country_code)
        settingsAPI.getVerifiedCallerIds()
        .then((res) => {
          let callerIdOptions: ISRDropdownOption[] = [] as ISRDropdownOption[]
          callerIdOptions.push({
            value: '',
            displayText: '---Select CallerId---',
            displayElement: <div>---Select CallerId---</div>
          })
          res.data.caller_ids.forEach(cid => {
            const option: ISRDropdownOption = {
              value: cid.uuid,
              displayText: cid.name + `<${cid.phone_number}>`,
              displayElement: <div className="flex justify-between">
                <span>{cid.name}</span>
                <span> &lt; {cid.phone_number} &gt;</span>
                </div>
            }
            callerIdOptions.push(option)

          })
          this.setState({
            callerid_options: callerIdOptions
          })
        })


      })
    }))
    .catch((e) => {
      console.log("error fetching countries")
    })

  }

  onCountrySelectionChange(country: ISRDropdownOption)
  {
    console.log("country.value", country.value.toString())
    const countryCode = country.value.toString();
    const isTrialUser = this.props.logInStore?.accountInfo.org.plan.plan_type === "trial";
    const isSubscriptionRequired = isTrialUser && countryCode !== "US" && countryCode !== "GB";

    this.setState({
      selected_country_code: countryCode,
      show_subscription_required: isSubscriptionRequired
    }, ()=> this.getPricingAPI(this.state.selected_country_code) )

  }

  onCalleridSelectionChange(callerid: ISRDropdownOption) {
    const callerId = callerid.value.toString().trim()
    this.setState({ selected_caller_id: callerId === '' ? undefined : callerId })

  }

  openUpgradeModal() {
    this.setState({ show_upgrade_modal: true });
  }

  closeUpgradeModal() {
    this.setState({ show_upgrade_modal: false });
  }

  modalHeader(isEditing: boolean) {
   const header = isEditing ? "Edit Phone Number Settings" : "Get Your Calling Number"
   return <div className='sr-h3 flex gap-2'><SRIconPhone className="text-sr-primary-80 h-6 w-6"/> {header}</div>
  }

  render() {
    const isEditing=this.props.isEditing
    const hasPhoneNumberAlready = this.props.isEditing && !!this.props.selectedPhoneNumber.phone_number
    const showCallerIdOptions = this.props.logInStore?.accountInfo.org.org_metadata.enable_callerid_verification;
    const isTrialUser = this.props.logInStore?.accountInfo.org.plan.plan_type === "trial";

    return (
      <>
        {!this.state.isLoading &&

          <SrModal
          size="small"
          title={this.modalHeader(isEditing)}
          onClose= {this.props.onClose}
          content = {
            <div className="py-[16px]">
            {hasPhoneNumberAlready &&
              <div className="border border-sr-gray-20 bg-sr-gray-10 rounded-lg p-[8px] grid grid-cols-2 gap-4 mb-4">

                <div>
                <div className="sr-p-basic text-sr-gray-100">
                  Number:
                </div>
                <div className="sr-h6  text-sr-gray-100">
                  {this.props.selectedPhoneNumber.phone_number}
                </div>
                </div>

                                 <div>
                   <div className="sr-p-basic text-sr-gray-100">
                     Country:
                   </div>
                   <div className="sr-h6  text-sr-gray-100">
                     {this.state.country_options.find(c => c.value === this.state.selected_country_code)?.displayText}
                   </div>
                 </div>
              </div>

            }

            <div className="flex flex-row">

              <div className="w-full mr-[8px]">

            <SRInput
              type="text"
              name="first_name"
              label="First name"
              placeholder="John"
              handleChange={(e) => this.onChangeFirstName(e)}
              selectedValue={this.state.first_name}
              width="fluid"
              autoFocus
              className="mb-4"
            />
            {this.state.show_first_name_empty_error &&
              <div className="text-sr-default-red -mt-3 mb-4">First name cannot be empty.</div>}
                </div>

            <div className="w-full ml-[8px]">

              <SRInput
                type="text"
                name="last_name"
                label="Last name"
                placeholder="Doe"
                handleChange={(e) => this.onChangeLastName(e)}
                selectedValue={this.state.last_name}
                width="fluid"
                className="mb-4"
              />
              {this.state.show_last_name_empty_error &&
                <div className="text-sr-default-red -mt-3 mb-4">Last name cannot be empty.</div>}
              </div>
            </div>
              <div className="w-full">
              <SRInput
                type="number"
                name="call_limits"
                label="Daily call task limit"
                labelTooltip="Maximum automated call tasks per day that Smartreach can create via campaigns on this number. You can always make unlimited manual calls beyond this limit."
                handleChange={(e) => this.onChangeCallLimit(e)}
                selectedValue={this.state.call_limit.toString()}
                width="fluid"
                className="mb-4"
              />
              </div>

            <div>

            <div className="mb-4">
            <div className="sr-p-basic text-sr-gray-90 block mb-1">
              Enable call forwarding ?
            </div>
            <div
              className={
                "grid grid-cols-3 gap-4"
              }
            >
              <div className="form-check">
                <input
                  className="form-check-input appearance-none rounded-full h-4 w-4 border border-sr-border-grey bg-white checked:sr-default-blue checked:border-default-blue focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                  type="radio"
                  name="flexRadioDefault"
                  id="flexRadioDefault3"
                  value="send"
                  checked={this.state.enable_forwarding}
                  onChange={()=> this.setState({enable_forwarding: !this.state.enable_forwarding})}
                />
                <label
                  className="form-check-label inline-block sr-p-basic text-sr-gray-100"
                  htmlFor="flexRadioDefault3"
                >
                  Yes
                </label>
              </div>
              <div className="form-check">
                <input
                  className="form-check-input appearance-none rounded-full h-4 w-4 border border-sr-border-grey bg-white checked:bg-sr-default-blue checked:border-sr-default-blue focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                  type="radio"
                  name="flexRadioDefault"
                  id="flexRadioDefault2"
                  value="receive"
                  checked={!this.state.enable_forwarding}
                  onChange={()=> this.setState({ enable_forwarding: !this.state.enable_forwarding })}
                />
                <label
                  className="form-check-label inline-block sr-p-basic text-sr-gray-100"
                  htmlFor="flexRadioDefault2"
                >
                  No
                </label>
              </div>
            </div>
            </div>

            {this.state.enable_forwarding && <SRInput
              type="text"
              name="forwarding_number"
              label="Forwarding number"
              placeholder="+91981XXXXXXX"
              handleChange={(e) => this.onChangeForwardingNumber(e)}
              labelTooltip="Any call back from a customer will be forwarded to this number"
              selectedValue={this.state.forwarding_number}
              width="fluid"
              className="mb-4"
            />}

            <div className="mb-4">
            <div className="sr-p-basic text-sr-gray-90 block mb-1">
              Enable call recording on this number ?
            </div>
            <div
              className={
                "grid grid-cols-3 gap-4"
              }
            >
              <div className="form-check">
                <input
                  className="form-check-input appearance-none rounded-full h-4 w-4 border border-sr-border-grey bg-white checked:sr-default-blue checked:border-default-blue focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                  type="radio"
                  name="flexRadioDefault2"
                  id="flexRadioDefault4"
                  value="send"
                  checked={this.state.enable_call_recording}
                  onChange={()=> this.setState({enable_call_recording: !this.state.enable_call_recording})}
                />
                <label
                  className="form-check-label inline-block sr-p-basic text-sr-gray-100"
                  htmlFor="flexRadioDefault4"
                >
                  Yes
                </label>
              </div>
              <div className="form-check">
                <input
                  className="form-check-input appearance-none rounded-full h-4 w-4 border border-sr-border-grey bg-white checked:bg-sr-default-blue checked:border-sr-default-blue focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                  type="radio"
                  name="flexRadioDefault2"
                  id="flexRadioDefault5"
                  value="receive"
                  checked={!this.state.enable_call_recording}
                  onChange={()=> this.setState({ enable_call_recording: !this.state.enable_call_recording })}
                />
                <label
                  className="form-check-label inline-block sr-p-basic text-sr-gray-100"
                  htmlFor="flexRadioDefault5"
                >
                  No
                </label>
              </div>
            </div>
            </div>
            {!hasPhoneNumberAlready && <div className="mb-4">
            <div className="block sr-p-basic text-sr-gray-90 mb-1">
              {this.props.isEditing? "Country": "Select country"}
            </div>

            <SRSearchDropdown
            options={this.state.country_options}
            selectedValue={this.state.selected_country_code}
            handleChange={this.onCountrySelectionChange}
            width='fluid'
            largerFontSize
            disabled={hasPhoneNumberAlready}
            />
            </div>}

            {this.state.show_subscription_required &&
              <div className="bg-sr-light-red rounded-lg p-[8px] my-4 w-full">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-5 w-5 mt-0.5">
                    <svg className="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3 flex-1">
                    <h3 className="text-sm font-medium text-red-800">
                      Subscription Required
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>
                        Phone numbers from {this.state.country_options.find(c => c.value === this.state.selected_country_code)?.displayText?.split('(')[0]} are only available with our Sales Engagement plan.
                        Upgrade to access premium calling features and numbers from additional countries.
                      </p>
                    </div>
                    <div className="mt-4 flex">
                      <SRButtonFilled
                        text="Upgrade to Sales Engagement"
                        onClick={this.openUpgradeModal}
                        isNegative={true}
                      />
                    </div>
                  </div>
                </div>
              </div>
            }
            {isTrialUser && !this.state.show_subscription_required &&
            <div className="flex border border-sr-primary-20 bg-sr-primary-10 rounded-lg p-[8px] my-4 w-full">
                <div><SRIconEnhance className="text-sr-primary-100 h-6 w-6"/></div>
                <div className="sr-p-basic text-sr-primary-100">
                  <div className="sr-p-normal">Trial Account Benefits</div>
                   <div>You have <span className="font-bold">$5 USD worth of calling balance</span> during your trial!</div>
                </div>
            </div>

            }

            {this.state.show_pricing && !this.state.show_subscription_required &&

            // <div className="bg-sr-light-yellow rounded-[4px] p-[8px] my-4 w-full">
            //   <div className="text-sr-text-grey sr-h6 !font-normal">
            //       Your organization will be billed {this.state.price_of_number} {this.state.currency} per month for this number.
            //   </div>

            // </div>

            <div className="bg-sr-gray-10 border border-sr-gray-20 flex gap-2 rounded-[4px] p-[8px] my-4 w-full">
              <div><CreditCard className="w-5 h-5 text-gray-600 mt-0.5 flex-shrink-0" /></div>
              <div>
                <div className="sr-p-normal text-sr-gray-100">Pricing Details</div>
                <div className="text-sr-gray-100 sr-p-basic">
                  Your organization will be billed {this.state.price_of_number} {this.state.currency} per month for this number.
                </div>
              </div>

            </div>
            }

              {/* {this.state.show_error_message &&

                <div className="bg-sr-light-red rounded-[4px] p-[8px] my-[16px] w-full">
                  <div className="flex flex-row">
                    <div className="flex-1">
                  <p className="text-sr-text-white sr-h6 !font-normal">
                    In certain countries, local regulations may restrict the direct purchase of phone numbers. In such cases, please contact our <a onClick={crispShowChatBox}>support team</a>, and we will help you purchase the number.
                  </p></div>
                    <div className="content-center">
                      <SRButtonOutline
                      text="Contact us"
                      onClick={crispShowChatBox}
                      className='bg-white'
                      />
                    </div>
                  </div>
                </div>
              } */}
              </div>

              {this.props.isEditing && hasPhoneNumberAlready && showCallerIdOptions &&
              <div>
                <div> Select callerId</div>
                <div>

                    <SRSearchDropdown
                      options={this.state.callerid_options}
                      selectedValue={this.state.selected_caller_id || ''}
                      handleChange={this.onCalleridSelectionChange}
                      width='fluid'
                      largerFontSize
                    />

                </div>

              </div>

              }

            { isEditing ?

            <div className="flex justify-end">
            <SRButtonFilled
              width="fluid"
              isPrimary={true}
              onClick={
                this.validateData
              }
              text={(this.props.selectedPhoneNumber.phone_number===undefined)?
                "Buy Number":"Update Settings"
              }
              icon= 'sr_icon_phone'
              className="!my-4"
              loading={this.state.isSubmitting}
              />
              </div>
            :

            <SRButtonFilled
              width="fluid"
              onClick = {
                    this.validateData
                  }
              isPrimary={true}
              className="!mt-4"
              icon= 'sr_icon_phone'
              text="Get my calling number"
              loading={this.state.isSubmitting}
              disable={this.state.show_subscription_required}
              />
          }


            </div>
          }

          />
        }

        {this.state.show_upgrade_modal && (
          <PlanBasedModalWrapper
            onClose={this.closeUpgradeModal}
            shouldDisableLowestBasePlans={false}
            shouldReloadPageOnSuccess={true}
            disableEmailOutreach={true}
          />
        )}
      </>
    )
  }
};

export const AddCallNumberModal = withRouter(inject('logInStore')(observer(AddCallNumberModalComponent)));