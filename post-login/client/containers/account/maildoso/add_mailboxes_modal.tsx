import { classN<PERSON>s, ISRDropdownOption, SRAvatar, SRButtonFilled, SRFormInput, SrIconDelete, SRMultiSelectDropdown, SRUploadCloudIcon } from '@sr/design-component';
import React, { useState, useEffect } from 'react';
import { ISelectedDomain } from './buy_new_domains';
import { PurchasedDomainAndEmailForFrontend } from './register_confirmation';
import { CONSTANTS } from '../../../data/constants';
import { Formik, Form } from 'formik';
import Dropzone from "react-dropzone";
import * as awsApi from '../../../api/aws';
import { alertStore } from '../../../stores/AlertStore';
import { validateDomainCoverage } from '../../../utils/domain-validation';
import { RecentPurchaseInfo } from '../../../api/emailInfra';

export interface EmailAddress {
  email: string;
  domain: ISRDropdownOption;
}

export interface Mailbox {
  firstName: string;
  lastName: string;
  emailPrefix: string;
  profilePicture?: string;
  emailAddresses: EmailAddress[];
}

interface MailboxCreationProps {
  selectedDomains: ISelectedDomain[];
  onContinue: () => void;
  currentMailboxes: Mailbox[];
  updateMailboxes: (updatedMailboxes: Mailbox[]) => void;
  isZapmailFlow: boolean;
  purchasedDomains: PurchasedDomainAndEmailForFrontend[];
  recentPurchases: RecentPurchaseInfo[];
}

interface IAddMailboxesFormik {
  first_name: string
  last_name: string
  email_address: string
  profile_picture?: string
}



const MailboxCreation: React.FC<MailboxCreationProps> = ({
  selectedDomains,
  onContinue,
  currentMailboxes,
  updateMailboxes,
  isZapmailFlow,
  purchasedDomains,
  recentPurchases,
}) => {
  const [selectedDomainOptions, setSelectedDomainOptions] = useState<ISRDropdownOption[]>([]);
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(undefined);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  const domainOptions: ISRDropdownOption[] = selectedDomains.map(domain => ({ value: !!domain.uuid ? domain.uuid: domain.name , displayText: domain.name }));

  // Set all domains as selected by default
  useEffect(() => {
    setSelectedDomainOptions(domainOptions);
  }, [selectedDomains]);

  // Helper function to get all existing emails from purchased domains and recent purchases
  const getAllExistingEmails = (): string[] => {
    const purchasedEmails = purchasedDomains.flatMap((domain) =>
      domain.emails.map((email) => email.emailAddress.toLowerCase())
    );

    /**
     * 19 Aug 2025
     *
     * In scenarios where an email has been recently purchased but the transaction hasn't settled yet,
     * the domain may not be present in the purchasedDomains object.
     *
     * To handle these cases, we also check recent purchases from the email infra logs table.
     */

    const recentPurchaseEmails = recentPurchases.flatMap((purchase) =>
      purchase.purchased_emails.map((email) => email.toLowerCase())
    );

    return [...purchasedEmails, ...recentPurchaseEmails];
  };

  const addMailbox = (data: IAddMailboxesFormik) => {
    const emailAddresses: EmailAddress[] = selectedDomainOptions.map(domain => ({
      email: `${data.email_address}@${domain.displayText}`,
      domain: domain
    }));

    // Filter out emails already in current session (validation already checked for duplicates)
    const currentSelectedEmailAddresses: string[] = currentMailboxes.flatMap(m => m.emailAddresses).map(em => em.email.toLowerCase());
    const emailAddressesFiltered: EmailAddress[] = emailAddresses.filter(em => 
      !currentSelectedEmailAddresses.includes(em.email.toLowerCase())
    );

    if (emailAddressesFiltered.length > 0) {
      const newMailbox: Mailbox = {
        firstName: data.first_name,
        lastName: data.last_name,
        emailPrefix: data.email_address,
        profilePicture: data.profile_picture,
        emailAddresses: emailAddressesFiltered
      };

      updateMailboxes([...currentMailboxes, newMailbox]);
      
    }
    
    // Reset the preview after adding
    setPreviewUrl(undefined);
  };

  // Function to upload file to cloud storage
  const uploadFileToCloudStorage = (signedUrl: string, file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('PUT', signedUrl);
      xhr.setRequestHeader('Content-Type', file.type);
      xhr.setRequestHeader('Content-Disposition', 'inline');

      xhr.send(file);

      xhr.addEventListener('load', () => {
        const linkUrl = xhr.responseURL.split('?')[0];
        resolve(linkUrl);
      });

      xhr.addEventListener('error', () => {
        reject('Unable to upload file. Please try again.');
      });
    });
  };

  const handleDrop = async (acceptedFiles: File[], form: any) => {
    if (acceptedFiles && acceptedFiles.length > 0) {
      const file = acceptedFiles[0];

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        console.error("File too large");
        return;
      }

      setIsUploading(true);

      try {
        // Generate a unique filename
        const randomString = Math.random().toString(36).substring(2, 15);
        const isProdction = window.location.origin.includes("smartreach.io");
        const imageFilename = 'new_' + Math.floor(Date.now()) + '_' + randomString + '_' + 'profile' + '_' + (isProdction ? '' : 'dev_' + file.name);

        // Get signed URL for upload using awsApi
        const response = await awsApi.getSignedUrl({ filename: imageFilename });
        const signedUrl = response.data.presigned_url;

        // Upload file to cloud storage
        const imageUrl = await uploadFileToCloudStorage(signedUrl, file);

        // Update preview with the cloud URL
        setPreviewUrl(imageUrl);

        // Update form values
        form.setFieldValue('profile_picture', imageUrl);
      } catch (error) {
        console.error("Error uploading image:", error);
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleRemoveImage = (form: any) => {
    setPreviewUrl(undefined);
    form.setFieldValue('profile_picture', undefined);
  };

  const removeEmailAddress = (mailboxIndex: number, emailIndex: number) => {
    const updatedMailboxes = [...currentMailboxes];
    const mailbox = {...updatedMailboxes[mailboxIndex]};

    // Remove the specific email address
    mailbox.emailAddresses = mailbox.emailAddresses.filter((_, idx) => idx !== emailIndex);

    // If no Email Accounts left, remove the entire mailbox
    if (mailbox.emailAddresses.length === 0) {
      updatedMailboxes.splice(mailboxIndex, 1);
    } else {
      updatedMailboxes[mailboxIndex] = mailbox;
    }

    updateMailboxes(updatedMailboxes);
  };

  const getTotalEmailCount = () => {
    return currentMailboxes.reduce((total, mailbox) => total + mailbox.emailAddresses.length, 0);
  };

  const validateDomainCoverageLocal = () => {
    return validateDomainCoverage(selectedDomains, currentMailboxes);
  };

  const validate = (data: IAddMailboxesFormik) => {
    const errors: IAddMailboxesFormik = {} as IAddMailboxesFormik

    if(data.first_name.length === 0) {
      errors.first_name = "Please enter the first name"
    }

    if(data.last_name.length === 0) {
      errors.last_name = "Please enter the last name"
    }

    if(data.email_address.length === 0) {
      errors.email_address = "Please enter the email name"
    }

    // Check for duplicate emails if username is provided
    if(data.email_address.length > 0) {
      const emailAddresses: EmailAddress[] = selectedDomainOptions.map(domain => ({
        email: `${data.email_address}@${domain.displayText}`,
        domain: domain
      }));

      // Get all existing emails (both from current session and purchased domains)
      const currentSelectedEmailAddresses: string[] = currentMailboxes.flatMap(m => m.emailAddresses).map(em => em.email.toLowerCase());
      const existingPurchasedEmails: string[] = getAllExistingEmails();
      const allExistingEmails: string[] = [...currentSelectedEmailAddresses, ...existingPurchasedEmails];

      // Check for conflicts with existing emails
      const conflictingEmails = emailAddresses.filter(em => 
        allExistingEmails.includes(em.email.toLowerCase())
      );

      if (conflictingEmails.length > 0) {
        const conflictingEmailsList = conflictingEmails.map(em => em.email).join(', ');
        errors.email_address = `Email address${conflictingEmails.length > 1 ? 'es' : ''} already exist${conflictingEmails.length > 1 ? '' : 's'}: ${conflictingEmailsList}`;
      }
    }

    return errors
  }

  return (
    <div className="ml-4 pl-[5px] pr-[20px] relative h-full">
      {/* Header */}
      {/* <div className="flex justify-between items-center mb-8">
        <h1 className="text-xl font-semibold text-gray-900">2. Setup Email Accounts</h1>
        <div className="space-x-4">
          <button
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"

          >
            Go to Previous Step
          </button>
          <button
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            onClick={() => onContinue(mailboxes)}
          >
            Go to Next Step
          </button>
        </div>
      </div> */}

      <div className="flex gap-8 mt-[24px]">
        {/* Left Column */}

        <Formik
          initialValues={{
            first_name: "",
            last_name: "",
            email_address: "",
            profile_picture: undefined
          }}
          onSubmit={(form, { resetForm }) => {
            addMailbox(form)
            setSelectedDomainOptions(domainOptions);
            resetForm()
          }}
          validate={validate}
        >

          {(form) => (
            <Form>
              <div>
                <div className='flex flex-col gap-[2px]'>
                  <div className="sr-h4">Create Email Accounts</div>
                  <div className="sr-p-basic text-sr-gray-90">Who will be sending emails from these domains?</div>
                </div>

                <div className="mt-[24px] p-[16px] border border-sr-gray-20 rounded-[8px] flex flex-col gap-[24px]">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="sr-p-basic text-sr-gray-90 mb-[6px]">First Name</div>
                      <SRFormInput
                        type='text'
                        placeholder='Enter first name'
                        name='first_name'
                        autofocus
                      />
                      {/* <input
                  type="text"
                  placeholder="Enter first name"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                /> */}
                    </div>
                    <div>
                      <div className="sr-p-basic text-sr-gray-90 mb-[6px]">Last Name</div>
                      <SRFormInput
                        type='text'
                        placeholder='Enter last name'
                        name='last_name'
                      />
                    </div>
                  </div>

                  <div>
                    <div className="sr-p-basic text-sr-gray-90 mb-[6px]">Email Username</div>
                    <div className="flex gap-2">
                      <SRFormInput
                        type='text'
                        placeholder='yourname'
                        inputTooltipMessage='This <NAME_EMAIL>'
                        name='email_address'
                      />

                      <SRMultiSelectDropdown
                        options={domainOptions}
                        selectedOptions={selectedDomainOptions}
                        handleChange={(options) => setSelectedDomainOptions(options as ISRDropdownOption[])}
                        placeholder="All available domains"
                        onClose={() => console.log('debug close')}
                      />
                    </div>
                  </div>

                  { isZapmailFlow && <div>
                    <div className="sr-p-basic text-sr-gray-90 mb-[6px]">Profile Picture (Optional)</div>

                    {previewUrl ? (
                      <div className="mb-4">
                        <div className="flex flex-col items-center">
                          <div className="relative w-24 h-24 mb-2">
                            <img
                              src={previewUrl}
                              alt="Profile"
                              className="w-full h-full object-cover rounded-full border border-gray-200"
                            />
                            <button
                              type="button"
                              onClick={() => handleRemoveImage(form)}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center"
                              aria-label="Remove image"
                            >
                              ×
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : isUploading ? (
                      <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-md">
                        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mb-2"></div>
                        <p className="text-sm text-gray-500">Uploading image...</p>
                      </div>
                    ) : (
                      <Dropzone
                        accept="image/*"
                        onDrop={(acceptedFiles: File[]) => handleDrop(acceptedFiles, form)}
                        multiple={false}
                        className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center cursor-pointer hover:bg-gray-50"
                      >
                        <div>
                          <SRUploadCloudIcon classes="mx-auto mb-2 text-gray-400" />
                          <p className="text-sm text-gray-500">
                            Drag & drop an image here, or click to select
                          </p>
                          <p className="text-xs text-gray-400 mt-1">
                            JPG, PNG or GIF, max 5MB
                          </p>
                        </div>
                      </Dropzone>
                    )}
                  </div>}

                  <SRButtonFilled
                    className={classNames('px-[48px] py-[8px] text-[14px] !font-semibold !leading-[20px]')}
                    text={`Create ${(selectedDomainOptions.length == 1) ? "1 Email Account" : `${selectedDomainOptions.length} Email Accounts`}`}
                    type="submit"
                    isPrimary
                    width='fluid'
                    disable={!form.values.email_address || !form.values.first_name || !form.values.last_name || isUploading}
                  />
                </div>
              </div>
            </Form>
          )}

        </Formik>

        {/* Right Column */}
        <div className='flex-1 flex flex-col gap-[24px]'>
          <div className='flex justify-between items-center'>
            <div className='flex flex-col gap-[2px]'>
              <div className="sr-h4">Your New Email Accounts</div>
              <span className="sr-p-basic text-sr-gray-90">
                {getTotalEmailCount()} emails selected
              </span>
            </div>
            <SRButtonFilled
              isGreen
              text='Review and Purchase'
              className='w-[256px] !font-semibold !leading-[24px] !text-[16px]'
              onClick={() => {
                const validation = validateDomainCoverageLocal();
                if (!validation.isValid) {
                  alertStore.pushAlert({
                    message: `Please create at least one mailbox for the following domains: ${validation.uncoveredDomains.join(', ')}`,
                    status: 'error'
                  });
                  return;
                } else{
                  onContinue();
                }
              }}
              disable={currentMailboxes.length === 0}
            />
          </div>
          {/* <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-900">Email Accounts</h2>
            <span className="text-sm text-gray-600">{getTotalEmailCount()} emails selected</span>
          </div> */}

          <div className="">
            <div className="bg-sr-gray-10 rounded-md py-[8px] px-[24px] sr-h6 flex gap-[32px]">
              <div className='w-3/5'>Email Accounts</div>
              <div className='flex-1'>Sender</div>
            </div>
            {/* <div className="mb-2 pb-2 border-b border-gray-200 grid grid-cols-2">
              <span className="text-sm font-medium text-gray-700">Email Accounts</span>
              <span className="text-sm font-medium text-gray-700">Sender</span>
            </div> */}



            {currentMailboxes.length > 0 && (
              <>
                <div className="overflow-auto h-[292px]">
                  {currentMailboxes.map((mailbox, mailboxIndex) => {
                    return (
                      mailbox.emailAddresses.map((emailAddress, emailIndex) => (
                        <div key={`${mailboxIndex}-${emailIndex}`} className="flex gap-[32px] items-center py-[16px] px-[24px] border-b">
                          <div className="w-3/5 flex items-center truncate">
                            {mailbox.profilePicture ? (
                              <img
                                src={mailbox.profilePicture}
                                alt="Profile"
                                className="w-8 h-8 rounded-full mr-2 object-cover border border-sr-gray-20"
                              />
                            ) : (
                                <SRAvatar
                                name={{
                                  identifier: emailAddress.email,
                                  first_name: mailbox.firstName,
                                  last_name: mailbox.lastName
                                }}
                                className="mr-2"
                                size="sm"
                              />
                            )}
                            {emailAddress.email}
                          </div>
                          <div className='flex-1 flex justify-between'>
                            <div className="flex items-center">
                              <span>{mailbox.firstName} {mailbox.lastName}</span>
                            </div>
                            <div className='w-[20px] h-[20px] cursor-pointer text-sr-grey-80' onClick={() => removeEmailAddress(mailboxIndex, emailIndex)}>
                              <SrIconDelete />
                            </div>
                          </div>
                        </div>
                      ))
                    )
                  })}
                </div>
              </>
            )}



            {!currentMailboxes.length && (
              <div className="flex flex-col items-center w-[340px] m-auto mt-[70px] gap-[40px]">
                <div className="">
                  <img
                    src={CONSTANTS.CDN_URL + '/assets/integrations/email_24_dec_2024_maildoso.svg'}
                    alt="email illustration"
                    className="w-[64px] h-[64px] grayscale opacity-50"
                  />
                </div>

                {/* Text Content */}
                <div className='flex flex-col gap-[32px]'>
                  <div className='text-center'>
                    <div className="text-sr-grey-80 sr-p-normal">
                    Please enter your desired emails in the input box
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MailboxCreation;
