import { <PERSON><PERSON><PERSON><PERSON>Filled, SRTooltip2, SrIconTickCircle, SrModal } from '@sr/design-component';
import React from 'react';
import { CONSTANTS } from '../../../data/constants';
import { ZapmailWorkspaceType, emailInfraPlatformType } from '../../../api/emailInfra';
import { checkBillingPermission } from '../../../utils/permissions_related';
import { logInStore } from '../../../stores/LogInStore';


export const EmailPlansComponent: React.FC<{
  onSelectProvider: (provider: emailInfraPlatformType, workspaceType?: ZapmailWorkspaceType) => void;
}> = (props) => {
  const permissions = logInStore.getTeamRolePermissions.permissions;

  const account = logInStore.getAccountInfo;

  const isAgencyAdmin = (account.account_type === 'agency') && (logInStore.roleIsOrgOwnerOrAgencyAdminForAgency);

  const canManageBilling = checkBillingPermission(
    permissions.manage_billing,
    isAgencyAdmin
  );

  const canBuyDomainsAndEmailsForMaildoso = canManageBilling;

  const canBuyDomainsAndEmailsForZapmail = canManageBilling;

  return (
    <div className="py-4 mt-4">
      <div className="h-full grid px-6 justify-start items-start gap-6 grid-cols-2">
        {/* Google Workspace card */}
        <div className="grow shrink h-full basis-0 p-6 rounded-lg border border-[#e0e4ea] flex flex-col justify-start items-start gap-8 overflow-hidden">          <div className="self-stretch flex-col justify-start items-center gap-4 flex">
            <img
              src={CONSTANTS.CDN_URL + "/assets/email_integrations/smartreach_google_workspace.png"}
              alt="Google Workspace"
              className="h-32 mb-2"
            />
            <div className="text-[#223758] text-xl font-semibold font-sans leading-loose">Google Workspace</div>
          </div>
          <div className="self-stretch h-24 flex-col justify-start items-start gap-1 flex">
            <div className="self-stretch flex-col justify-start items-start gap-4 flex">
              <div className="self-stretch flex-col justify-start items-start gap-4 flex">
                <div className="self-stretch justify-start items-start gap-2.5 inline-flex">
                  <div className="flex-col justify-center w-fit mx-auto items-start gap-2 inline-flex">
                    <FeatureRow label="Managed by" value="Zapmail" />
                    <FeatureRow label="Domain" value="$5/month" />
                    <FeatureRow label="Email" value="$4/month" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex-1"></div>
          <div className="self-stretch justify-end items-center inline-flex mt-4">
            {/* <div className="text-center cursor-pointer text-[#0f69fa] text-sm font-semibold font-sans leading-tight">Learn more</div> */}
            {!canBuyDomainsAndEmailsForZapmail ?
              (
                <SRTooltip2 text="Please ask your admin to buy addons" direction="top center">
                  <div>
                    <SRButtonFilled onClick={() => props.onSelectProvider('zapmail', 'GOOGLE')} isPrimary text="Continue with Google" className='sr-filled-button-lg' disable />
                  </div>
                </SRTooltip2>)
              : <SRButtonFilled onClick={() => props.onSelectProvider('zapmail','GOOGLE')} isPrimary text="Continue with Google" className='sr-filled-button-lg' />
            }
          </div>
          {/* <div className="text-[#223758] text-xs font-normal font-sans leading-[18px]">*depends on the types of selected domains</div> */}
        </div>

        {/* Microsoft 365 card */}
        {/* 

          22 Aug 2025

          We are not supporting Microsoft 365 for now.

        */}
        {/* <div className="grow shrink h-full basis-0 p-6 rounded-lg border border-[#e0e4ea] flex flex-col justify-start items-start gap-8 overflow-hidden">          <div className="self-stretch flex-col justify-start items-center gap-4 flex">
            <img
              src={CONSTANTS.CDN_URL + "/assets/home_page_sp_office365_2025.png"}
              alt="Microsoft 365"
              className="h-32 mb-2"
            />
            <div className="text-[#223758] text-xl font-semibold font-sans leading-loose">Microsoft 365</div>
          </div>
          <div className="self-stretch h-24 flex-col justify-start items-start gap-1 flex">
            <div className="self-stretch flex-col justify-start items-start gap-4 flex">
              <div className="self-stretch flex-col justify-start items-start gap-4 flex">
                <div className="self-stretch justify-start items-start gap-2.5 inline-flex">
                  <div className="flex-col justify-center w-fit mx-auto items-start gap-2 inline-flex">
                    <FeatureRow label="Managed by" value="Zapmail" />
                    <FeatureRow label="Domain" value="$5/month" />
                    <FeatureRow label="Email" value="$4/month" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="text-xs text-sr-warning-80 bg-sr-lightest-yellow border border-sr-warning-50 rounded-md p-2">
            <p>
              Note : Warmup is not supported for Microsoft 365.
            </p>
          </div>
          <div className="flex-1"></div>
          <div className="self-stretch justify-end items-center inline-flex mt-4">
            {!canBuyDomainsAndEmailsForZapmail ?
              (
                <SRTooltip2 text="Please ask your admin to buy addons" direction="top center">
                  <div>
                    <SRButtonFilled onClick={() => props.onSelectProvider('zapmail', 'MICROSOFT')} isPrimary text="Continue with Microsoft 365" className='sr-filled-button-lg' disable />
                  </div>
                </SRTooltip2>)
              : <SRButtonFilled onClick={() => props.onSelectProvider('zapmail', 'MICROSOFT')} isPrimary text="Continue with Microsoft 365" className='sr-filled-button-lg' />
            }
          </div>
        </div> */}

        {/* SMTP card */}
        <div className="grow shrink h-full basis-0 self-stretch p-6 rounded-lg border border-[#e0e4ea] flex flex-col justify-start items-start gap-8 overflow-hidden">
          <div className="self-stretch flex-col justify-start items-center gap-4 flex">
            <img
              src={CONSTANTS.CDN_URL + "/assets/home_page_sp_smtp_imap.png"}
              alt="SMTP"
              className="h-32 object-contain mb-2"
            />
            <div className="text-[#223758] text-xl font-semibold font-sans leading-loose">SMTP</div>
          </div>
          <div className="self-stretch h-24 flex-col justify-start items-start gap-1 flex">
            <div className="self-stretch flex-col justify-start items-start gap-4 flex">
              <div className="self-stretch flex-col justify-start items-start gap-4 flex">
                <div className="self-stretch justify-start items-start gap-2.5 inline-flex">
                  <div className="flex-col justify-center w-fit mx-auto items-start gap-2 inline-flex">
                    <FeatureRow label="Managed by" value="Maildoso" />
                    <FeatureRow label="Domain" value="$4/month" />
                    <FeatureRow label="Email" value="$3/month" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex-1"></div>
          <div className="self-stretch justify-end items-center inline-flex mt-4">
            {/* <div className="text-center cursor-pointer text-[#0f69fa] text-sm font-semibold font-sans leading-tight">Learn more</div> */}
            {/* <SRButtonFilled onClick={() => props.onSelectProvider('maildoso')} isPrimary text="Continue with SMTP" className='sr-filled-button-lg' /> */}
            {!canBuyDomainsAndEmailsForMaildoso ?
              (
                <SRTooltip2 text="Please ask your admin to buy addons" direction="top center">
                  <div>
                    <SRButtonFilled onClick={() => props.onSelectProvider('maildoso')} isPrimary text="Continue with SMTP" className='sr-filled-button-lg' disable />
                  </div>
                </SRTooltip2>)
              : <SRButtonFilled onClick={() => props.onSelectProvider('maildoso')} isPrimary text="Continue with SMTP" className='sr-filled-button-lg' />
            }
          </div>
        </div>
      </div>
    </div>
  );
};

interface FeatureRowProps {
  label: string;
  value: string;
}

const FeatureRow: React.FC<FeatureRowProps> = ({ label, value }) => {
  return (
    <div className="self-stretch justify-start items-center gap-1 inline-flex">
      <div className="relative">
        <SrIconTickCircle className='h-8 w-8 pr-2 text-green-400' />
      </div>
      <div className="justify-start items-center gap-4 flex">
        <div className="w-24 text-[#223758] text-sm font-semibold font-sans leading-tight">{label}</div>
        <div className="text-[#223758] text-sm font-normal font-sans leading-tight">{value}</div>
      </div>
    </div>
  );
};

export const ChooseDomainServiceModal = (
  props: {
    onClose: () => void,
    onSelectProvider: (provider: emailInfraPlatformType, workspaceType?: ZapmailWorkspaceType) => void,
  }
) => {
  return <SrModal
    onClose={props.onClose}
    size='small-plus'
    title={<h3>Choose your preferred Email Service Provider</h3>}
    content={
      <EmailPlansComponent
        onSelectProvider={props.onSelectProvider}
      />
    }
  />
}
