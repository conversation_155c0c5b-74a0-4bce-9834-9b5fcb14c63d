import React, { useEffect, useState } from 'react';
// import SearchDomains from './search_domains';
// import DomainAvailability from './domain_availability';
import RegistrationConfirmation, {  EmailInfraWorkspaceType, PurchasedDomain, PurchasedDomainAndEmailForFrontend } from './register_confirmation';
import RegisterationInProgress from './registration_in_progress';
import MailboxCreation, { Mailbox } from './add_mailboxes_modal';
import { logInStore } from '../../../stores/LogInStore';
import { emailInfraPlatformType, fetchAllDomainsAndEmails, saveAdditionalEmail, saveDomains, searchDomains, searchZapmailDomains, zapMailSaveAdditionalDomains, zapMailSaveDomains, getRecentPurchases, RecentPurchaseInfo } from '../../../api/emailInfra';
import NoDomainFound from './no_domains_found';
import CombinedDomainSearch from './maildoso_combined_componet';
import { PurchasedDomainsTable } from './purchase_domains_table';
import { CONSTANTS } from '../../../data/constants';
import PurchaseSuccessModal from './purchase_successful_modal';
import ZapMailCombinedDomainSearch from './zapmail_combined_component';
import { ChooseDomainServiceModal } from './choose_domain_provider';
import { SRLineTabsNew } from '../../../components/sr-tabs';
import PurchaseDomainRedirectUrl from './purchase_domain_redirect_url';
import { SrIconArrowLeft } from '@sr/design-component';
import { SRSpinner } from '@sr/design-component';
// import UserDetails from './add_user_details_for_domain';
import * as emailInfraApi from "./../../../api/emailInfra"

export interface SearchResult{
    name: string,
    status?: "available" | "unavailable",

    /*
    "yes" -> domain is available
    "no" | null -> domain is not available
    */
    available?: "yes" | "no"
    error?: string
}

export interface DomainSearchResult{
  domains: SearchResult[];
}

export interface ZapmailDomainSearchResult {
  domainName: string,
  status: string
}

export interface ISelectedDomain {
  name: string,
  uuid?: string,
  platformType: emailInfraPlatformType,
  workspaceType: EmailInfraWorkspaceType
}

const SetupNewDomains: React.FC = () => {
  const [step, setStep] = useState(0);
  const [results, setResults] = useState<DomainSearchResult>({ domains: [] });
  const [zapMailresults, setZapMailResults] = useState<ZapmailDomainSearchResult[]>([]);
  const [selectedDomains, setSelectedDomains] = useState<ISelectedDomain[]>([]);
  const [redirectUrl, setRedirectUrl] = useState('');
  const [isCompleted, setIsCompleted] = useState(false);
  const [mailboxes, setMailboxes] = useState<Mailbox[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [hasExistingDomains, setHasExistingDomains] = useState(false);
  const [purchasedDomains, setPurchasedDomains] = useState<PurchasedDomainAndEmailForFrontend[]>([]);
  const [isAdditionalPurchaseFlow, setIsAdditionalPurchaseFlow] = useState(false);
  const [isSearchingDomains, setIsSearchingDomains] = useState(false)
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [purchasedData, setPurchasedData] = useState<PurchasedDomain[]>([])
  const [zapmailWorkspaceType, setZapmailWorkspaceType] = useState<emailInfraApi.ZapmailWorkspaceType>('GOOGLE');

  const [isZapmailPurchaseFlow, setIsZapmailPurchaseFlow] = useState(false);
  const [chooseDomainServiceModal, setChooseDomainServiceModal] = useState(false);
  const [recentPurchases, setRecentPurchases] = useState<RecentPurchaseInfo[]>([]);


  const fetchDomains = () => {
    setIsLoading(true)
    fetchAllDomainsAndEmails()
      .then((data) => {
        setPurchasedDomains(data)
        setHasExistingDomains(data && data.length > 0);
        setIsLoading(false);
      })
      .catch((error) => {
        console.error('Error fetching domains:', error);
        setIsLoading(false);
      });
  };

  /*
  const fetchAddonsCount = async () => {
    try {
      const [maildosoResult, zapmailResult] = await Promise.all([
        emailInfraApi.getAddonsCountForDomainsAndEmailsPurchase(
          0,
          0,
          'maildoso'
        ),
        emailInfraApi.getAddonsCountForDomainsAndEmailsPurchase(
          0,
          0,
          'zapmail'
        )
      ]);

      setmaildosoAddons(maildosoResult);
      setzapmailAddons(zapmailResult);

      return { maildoso: maildosoResult, zapmail: zapmailResult };
    } catch (error) {
      console.error('Error fetching addons count:', error);
      return null;
    }
  };
  */

  const fetchRecentPurchases = async () => {
    try {
      const purchases = await getRecentPurchases();
      setRecentPurchases(purchases);
    } catch (error) {
      console.error('Error fetching recent purchases:', error);
    }
  };

  useEffect(() => {

    fetchDomains();
    fetchRecentPurchases();
    // fetchAddonsCount();
  }, []);

  /*
    Date: 21-03-2025
    This is added to handle the back button in the browser
    step 0 has purchase table, and purchase happens from step 1
    so if we are purchase flow and clicked on back button
    then we need to go back to step 0 which is purchase table
  */
  useEffect(() => {
    if (step > 0) {
      window.history.pushState({ step }, '', window.location.pathname);
    }

    const handlePopState = (event: PopStateEvent) => {
      if (step > 0) {
        setStep(0);
      }
    };
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [step]);

  const handleStartNewDomain = () => {
    // logInStore.getAccountInfo.org.org_metadata.allow_user_level_api_key

    // Reset the states of purchase info
    setMailboxes([]);
    setSelectedDomains([]);
    setRedirectUrl('');

    // Open the domain service selection modal
    setChooseDomainServiceModal(true);
    setIsAdditionalPurchaseFlow(false);
  };

  const handleAddAdditionalEmail = (domain: ISelectedDomain) => {
    console.log('debug domain', domain);
    setSelectedDomains([domain]);
    setMailboxes([]);
    setIsAdditionalPurchaseFlow(true);
    if (domain.workspaceType !== 'SMTP'){
      setZapmailWorkspaceType(domain.workspaceType);
    }
    setIsZapmailPurchaseFlow(domain.platformType === 'zapmail')
    setStep(3)
  }

  const handleSearch = async (domains: string[]) => {
    setIsSearchingDomains(true)
    searchDomains(domains)
      .then((data) => {
        console.log('debug data', data);
        setResults(data);
      })
      .catch((err) => {
        // alert(`Error: ${err.message}`);
        // alert(`Error: ${err.message}`);
        console.log('error', err)
      })
      .finally(() => setIsSearchingDomains(false))

  };

  const handleZapmailSearch = async (domains: EmailInfra.IZapMailDomainRequest) => {
    setIsSearchingDomains(true)
    searchZapmailDomains(domains)
      .then((data) => {
        console.log('debug data', data);
        setZapMailResults(data);
      })
      .catch((err) => {
        // alert(`Error: ${err.message}`);
        // alert(`Error: ${err.message}`);
        console.log('error', err)
      })
      .finally(() => setIsSearchingDomains(false))

  };

  const filterOutMailboxesByDomain = (mailboxes: Mailbox[], domainToRemove: string): Mailbox[] => {
    return mailboxes
      .map((mailbox) => ({
        ...mailbox,
        emailAddresses: mailbox.emailAddresses.filter(
          (emailAddress) => emailAddress.domain.displayText !== domainToRemove
        ),
      }))
      .filter((mailbox) => mailbox.emailAddresses.length > 0); // Remove mailboxes with no email addresses
  };

  const updateSelectedDomains = (domain: string) => {
    setSelectedDomains((prev) => {

      const prevSelectedDomainNames = prev.map(d => d.name)

      const newDomainSelected: ISelectedDomain = {
        name: domain,
        platformType: isZapmailPurchaseFlow ? 'zapmail' : 'maildoso',
        workspaceType: isZapmailPurchaseFlow ? zapmailWorkspaceType : 'SMTP'
      }

      if(prevSelectedDomainNames.includes(domain)) {
        const mailboxesFiltered = filterOutMailboxesByDomain(mailboxes, domain)
        setMailboxes(mailboxesFiltered)
        return prev.filter((item) => item.name !== domain)
      }

      return [...prev, newDomainSelected]

    });
  }

  const updateMailboxes = (updatedMailboxes: Mailbox[]) => {
    setMailboxes(updatedMailboxes)
  }


  const handleContinueFromDomains = () => {
    // console.log('debug selected', selected, 'redirectUrl', redirectUrl);
    // setRedirectUrl(redirectUrl);

    setStep(2);
  };

  const handleContinueFromMailboxes = () => {
    setStep(4);
  };

  // const handleContinueFromUserDetails = (details: { ownerName: string; contactEmail: string; phoneNumber: string; }) => {
  //   setUserDetails(details);
  //   setStep(5);
  // };

  // const mockApiCall = (delay: number = 2000): Promise<string> => {
  //   return new Promise((resolve, reject) => {
  //     setTimeout(() => {
  //       resolve("response");
  //     }, delay);
  //   });
  // };


  const handleRegister = async (toBePurchased: PurchasedDomain[], is_additional_email_flow: boolean = false,hasAcknowledged?:boolean) => {
    if (isZapmailPurchaseFlow) {
      handleZapmailRegister(toBePurchased, is_additional_email_flow,hasAcknowledged)
    } else {
      handleMailDosoRegister(toBePurchased, is_additional_email_flow)
    }
  }

  const handleMailDosoRegister = async (toBePurchased: PurchasedDomain[], is_additional_email_flow: boolean = false) => {

    setIsPurchasing(true);
    if(!is_additional_email_flow){

      saveDomains(toBePurchased, logInStore.getCurrentTeamId)
      .then((data) => {
        console.log('debugging toBePurchased', toBePurchased);
        setPurchasedData(toBePurchased)
        setIsPurchasing(false);
        setIsCompleted(true);
        setMailboxes([]);
        setSelectedDomains([]);
        setRedirectUrl('');
        fetchDomains()
        setShowSuccessModal(true);
        setIsAdditionalPurchaseFlow(false);
        setStep(0);

      }). catch((err) => {
        setIsPurchasing(false);
        // alert(`Error: ${err.message}`);
        console.log('error', err)
      });

    }else{

      const domainUuid = selectedDomains[0].uuid
      const emails = toBePurchased.map((email) => email.purchasedEmails).map((emails) => emails.map((email) => {

        const purchasedEmail:EmailInfra.IPurchaseEmail = {
          first_name: email.first_name,
          last_name: email.last_name,
          email_account: email.email_account,
          password: "dummy_password",
        }

        return purchasedEmail

      })).flat()

      console.log('debug flat emails', emails);

      saveAdditionalEmail(domainUuid, emails)
      .then((data) => {
        console.log('debug data', data);
        setIsPurchasing(false);
        setIsCompleted(true);
        setMailboxes([]);
        setSelectedDomains([]);
        setRedirectUrl('');
        setIsAdditionalPurchaseFlow(false);
        fetchDomains()
        setStep(0);

      }). catch((err) => {
        setIsPurchasing(false);
        // alert(`Error: ${err.message}`);
        console.log('error', err)
      });

    }

  };


  const handleZapmailRegister = async (toBePurchased: PurchasedDomain[], is_additional_email_flow: boolean = false,hasAcknowledged?:boolean) => {

    setIsPurchasing(true);
    if (!is_additional_email_flow) {
      let domainInfo: { [key: string]: EmailInfra.ZapMailMailboxInfo[] } = {};

      toBePurchased.forEach((domain) => {
        let domainEmailInfo = domain.purchasedEmails.map((emailInfo) => {
          return {
            username: emailInfo.email_account.split('@')[0].toLowerCase(),
            firstName: emailInfo.first_name,
            lastName: emailInfo.last_name,
            profilePicture: emailInfo.profilePicture
          }
        })

        domainInfo[domain.domainName] = domainEmailInfo
      })

      console.log('debugging domainInfo', domainInfo);

      zapMailSaveDomains(
        {
          domains: domainInfo,
          domainRedirectTo: redirectUrl,
          workspaceType: zapmailWorkspaceType,
          has_acknowledged: hasAcknowledged
        }, logInStore.getCurrentTeamId)
        .then((data) => {
          console.log('debugging toBePurchased', toBePurchased);
          setPurchasedData(toBePurchased)
          setIsPurchasing(false);
          setIsCompleted(true);
          setMailboxes([]);
          setSelectedDomains([]);
          setRedirectUrl('');
          setResults({ domains: [] });
          setZapMailResults([]);
          fetchDomains()
          setShowSuccessModal(true);
          setIsAdditionalPurchaseFlow(false);
          setStep(0);

        }).catch((err) => {
          setIsPurchasing(false);
          // alert(`Error: ${err.message}`);
          console.log('error', err)
        });

    } else {
      zapMailSaveAdditionalDomains(
        {
          domain: toBePurchased[0].domainName,
          emails: toBePurchased[0].purchasedEmails.map((emailInfo) => {
            return {
              username: emailInfo.email_account.split('@')[0].toLowerCase(),
              firstName: emailInfo.first_name,
              lastName: emailInfo.last_name,
              profilePicture: emailInfo.profilePicture
            }
          }),
          workspaceType: zapmailWorkspaceType,
          has_acknowledged: hasAcknowledged
        }, logInStore.getCurrentTeamId).then((data) => {
          console.log('debugging toBePurchased', toBePurchased);
          setPurchasedData(toBePurchased)
          setIsPurchasing(false);
          setIsCompleted(true);
          setMailboxes([]);
          setSelectedDomains([]);
          setRedirectUrl('');
          setResults({ domains: [] });
          setZapMailResults([]);
          fetchDomains()
          setShowSuccessModal(true);
          setIsAdditionalPurchaseFlow(false);
          setStep(0);

        }).catch((err) => {
          setIsPurchasing(false);
          // alert(`Error: ${err.message}`);
          console.log('error', err)
        });
    }

  };

  return (
    isLoading ? (
      <div className='w-full h-full items-center justify-center'>
                      <SRSpinner spinnerTitle="Loading..." />
                    </div>
    ) : (
    <div className='h-full flex flex-col'>
          {(step >= 1) &&
            <div className='flex flex-col'>
              <div className='flex justify-between border-b'>
                <div className='flex items-center justify-center gap-2'>
                <div className="px-2 flex items-center justify-center cursor-pointer" onClick={() => setStep(0)}><SrIconArrowLeft className='h-[24px] w-[24px]'/></div>
                <span className="text-2xl py-3 font-semibold text-gray-900 ">
                  {isAdditionalPurchaseFlow ? "Buy Additional Email" : "Buy Domains"} ({isZapmailPurchaseFlow ? (zapmailWorkspaceType === 'GOOGLE' ? "Google Workspace" : "Microsoft 365") : "SMTP"})
                </span>
                </div>
                {
                  <div className='mr-2 py-[10px] px-[16px] min-h-[65px] flex flex-col gap-[6px] rounded-[8px] border-sr-gray-20'>

                    <span className='sr-h7 text-sr-grey-80'>powered by</span>
                    <img src={
                      (isZapmailPurchaseFlow) ?
                        CONSTANTS.CDN_URL + '/assets/jan25/zapmail.png' :
                        CONSTANTS.CDN_URL + '/assets/integrations/maildoso_icon.png'
                    }
                      className='w-[83px] object-contain'
                    />
                  </div>
                }
              </div>
              {(isAdditionalPurchaseFlow) ?
              <SRLineTabsNew
                tabs={
                    [
                      {
                        displayName: 'Create Email Accounts',
                        value: 2,
                        isActive: step === 3,
                        isDone: step > 2
                      },
                      {
                        displayName: 'Review & Buy',
                        value: 3,
                        isActive: step === 4
                      }
                    ]}
                  navType="onclick"
                  onClick={(e) => {
                    if ((e as number) < step) {
                      setStep((e as number + 1));
                    }
                  }}
                  className="w-1/2 justify-center"
                />
                : <SRLineTabsNew
                  tabs={
                  [{
                    displayName: 'Find Available Domains',
                    value: 0,
                    isActive: step === 1,
                    isDone: step > 0
                  },
                  {
                    displayName: 'Configure Domain Settings',
                    value: 1,
                    isActive: step === 2,
                    isDone: step > 1
                  },
                  {
                    displayName: 'Create Email Accounts',
                    value: 2,
                    isActive: step === 3,
                    isDone: step > 2
                  },
                  {
                    displayName: 'Review & Buy',
                    value: 3,
                    isActive: step === 4
                  }
                  ]
                }
                navType="onclick"
                onClick={(e) => {
                  if ((e as number) < step){
                  setStep((e as number + 1) );}
                }}
                className="w-1/2 justify-center"
                />}
            </div>}
    {showSuccessModal && (
      <PurchaseSuccessModal
        onClose={() => setShowSuccessModal(false)}
              isZapmailPurchaseFlow={isZapmailPurchaseFlow}
        purchasedDetails={purchasedData}
      />
    )}
    {step === 0 && !hasExistingDomains && (
      <NoDomainFound onAddNewDomain={handleStartNewDomain}/>
    )}
    {step === 0 && hasExistingDomains && (
            <PurchasedDomainsTable
        domains={purchasedDomains}
        onAddNew={handleStartNewDomain}
        onAddNewEmail={handleAddAdditionalEmail}
       />
          )}
          {step === 1 && (isZapmailPurchaseFlow ? <ZapMailCombinedDomainSearch
            onSearch={handleZapmailSearch}
            searchResults={zapMailresults}
            onContinue={handleContinueFromDomains}
            isLoading={isSearchingDomains}
            updateSelectedDomains={updateSelectedDomains}
            selectedDomains={selectedDomains}
            recentPurchases={recentPurchases}
          /> : <CombinedDomainSearch
            onSearch={handleSearch}
          searchResults={results}
              onContinue={handleContinueFromDomains}
          isLoading={isSearchingDomains}
          updateSelectedDomains={updateSelectedDomains}
          selectedDomains={selectedDomains}
          recentPurchases={recentPurchases}
          />)}
      {/* {step === 2 && (
        <DomainAvailability results={results} onContinue={handleContinueFromDomains} />
      )} */}
      {
        step===2 && (
          <PurchaseDomainRedirectUrl
            redirect_url={redirectUrl}
            onContinue={
              (redirect_url) => {
                setRedirectUrl(redirect_url);
              setStep(3);
            }}
          />
        )
      }
      {step === 3 && (
        <MailboxCreation
              selectedDomains={selectedDomains}
              onContinue={handleContinueFromMailboxes}
          currentMailboxes={mailboxes}
              updateMailboxes={updateMailboxes}
              isZapmailFlow={isZapmailPurchaseFlow}
              purchasedDomains={purchasedDomains}
              recentPurchases={recentPurchases}
        />
      )}
      {step === 4 && (
        <RegistrationConfirmation
          selectedDomains={selectedDomains}
          isLoading={isPurchasing}
          mailboxes={mailboxes}
          redirectUrl={redirectUrl}
              isZapMailFlow={isZapmailPurchaseFlow}
          // userDetails={userDetails}
          costPerMailbox={(isZapmailPurchaseFlow)? CONSTANTS.zapmail.email_price : CONSTANTS.maildoso.email_price}
          domainCost={(isZapmailPurchaseFlow)? CONSTANTS.zapmail.domain_price : CONSTANTS.maildoso.domain_price}
          onRegister={handleRegister}
          emailWorkspaceType={(isZapmailPurchaseFlow)?zapmailWorkspaceType: 'SMTP'}
        />
      )}
      {step === 5 && <RegisterationInProgress isCompleted={isCompleted} />}

          {chooseDomainServiceModal && (
            <ChooseDomainServiceModal
              onClose={() => setChooseDomainServiceModal(false)}
              onSelectProvider={(provider: emailInfraPlatformType, workspaceType?: emailInfraApi.ZapmailWorkspaceType) => {
                if (provider === 'maildoso') {
                  setIsZapmailPurchaseFlow(false);
                } else {
                  setIsZapmailPurchaseFlow(true);
                  setZapmailWorkspaceType(workspaceType!);
                }
                setChooseDomainServiceModal(false);
                setStep(1);
              }}
            />
          )}
    </div>
    )
  );
};

export default SetupNewDomains;