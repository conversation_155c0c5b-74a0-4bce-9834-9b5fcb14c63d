import React from 'react';
import { SrModal } from '@sr/design-component';
import { CONSTANTS } from '../../../data/constants';
import { PurchasedDomain } from './register_confirmation';

interface PurchaseSuccessModalProps {
  onClose: () => void;
  purchasedDetails: PurchasedDomain[];
  isZapmailPurchaseFlow: boolean
}

const PurchaseSuccessModal: React.FC<PurchaseSuccessModalProps> = ({ onClose, purchasedDetails, isZapmailPurchaseFlow }) => {
  return (
    <SrModal
      onClose={onClose}
      size='medium'
      title=''
      content={
        <div className='flex flex-col p-[30px]'>
          <div className='self-center'>
            <img
              src={CONSTANTS.CDN_URL + '/assets/sr_success_check_icon.svg'}
              className='w-[100px] m-[10px]'
            />
          </div>
          <div className='text-sr-text-grey font-bold text-lg font-sourcesanspro self-center mb-[20px]'>
            Purchase Successful
          </div>

          <div className='max-h-[300px] overflow-y-auto'>
            {purchasedDetails.map((domain, index) => (
              <div key={index} className='border rounded-md p-4 mb-4'>
                <div className='font-semibold mb-2'>
                  Domain: {domain.domainName}
                </div>
                <div className='space-y-2'>
                  {domain.purchasedEmails.map((email, emailIndex) => (
                    <div key={emailIndex} className='flex items-center text-sm text-sr-gray-90'>
                      <span className='mr-2'>•</span>
                      {email.email_account} ({email.first_name} {email.last_name})
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className='bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4'>
            <div className='flex items-start gap-3'>
              <div className='text-blue-600 text-xl'>📧</div>
              <div>
                <div className='font-semibold text-blue-800 mb-2'>Important: Email Sending Guidelines</div>
                <div className='text-blue-700 text-sm'>
                  To maintain excellent deliverability, please don't send more than 10 emails for the first few days and gradually increase your sending volume over time.
                </div>
              </div>
            </div>
          </div>

          <div className='border py-[10px] px-[16px] flex flex-col gap-[6px] rounded-[8px] border-sr-gray-20 mt-[20px] self-center'>
            <span className='sr-h7 text-sr-grey-80'>powered by</span>
            <img
              src={
                    (isZapmailPurchaseFlow) ?
                      CONSTANTS.CDN_URL + '/assets/jan25/zapmail.png' :
                      CONSTANTS.CDN_URL + '/assets/integrations/maildoso_icon.png'
                  }
              className='w-[83px] object-contain'
            />
          </div>
        </div>
      }
    />
  );
};

export default PurchaseSuccessModal;