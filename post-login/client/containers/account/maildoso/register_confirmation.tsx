import React, { useEffect, useState } from 'react';
import { Mailbox } from './add_mailboxes_modal';
import { class<PERSON><PERSON>s, SRButtonFilled, SRCheckbox, SrIconHealthCheck, SrModal, SRSpinner } from '@sr/design-component';
import { ISelectedDomain } from './buy_new_domains';
import { CONSTANTS } from '../../../data/constants';
import { emailInfraPlatformType, getAddonsCountForDomainsAndEmailsPurchase, getRequiredAddonsCountForDomainsAndEmailsPurchase, IRequiredEmailAndDomains } from '../../../api/emailInfra';
import * as billingApi from '../../../containers/billing/client/api/billing'
import { sortBy} from 'lodash';
import UpdateSubscriptionAddons from '../../billing/client/components/billing_v2/update-subscription-addons';
import { logInStore } from '../../../stores/LogInStore';
import PlanNotSupportedForEmailAndDomainsPurchase from '../../billing/client/components/billing_v2/plan-not-support-for-emails-and-domains-purchase';

interface RegistrationConfirmationProps {
  isLoading: boolean;
  selectedDomains: ISelectedDomain[];
  mailboxes: Mailbox[];
  costPerMailbox: number;
  domainCost: number;
  redirectUrl: string;
  onRegister: (purchasedDomains: PurchasedDomain[], is_additional_email_flow: boolean,hasAcknowledged?:boolean) => Promise<void>;
  isZapMailFlow: boolean,
  emailWorkspaceType: EmailInfraWorkspaceType
}

export interface EmailDetails {
  emailSettingId?: number;
  emailSettingUuid?: string;
  emailAddress: string;
  emailStatus?: IDomainAndEmailStatus;
  senderFirstName: string;
  senderLastName: string;
  emailCreatedAt?: Date;
  profilePictureUrl?: string;
}

export type IDomainAndEmailStatus = 'active' | 'setup_initiated' | 'setup_pending' | 'scheduled_for_deletion' | 'deleted' | 'waiting_for_zapmail_order_success_webhook';

export type EmailInfraWorkspaceType = 'GOOGLE' | 'MICROSOFT' | 'SMTP'

export interface PurchasedDomainAndEmailForFrontend {
  purchasedDomainUuid: string;
  domainName: string;
  domainStatus: IDomainAndEmailStatus;
  emails: EmailDetails[];
  platformType: emailInfraPlatformType;
  workspaceType: EmailInfraWorkspaceType;
}



export interface PurchasedDomain {
  domainName: string;
  purchasedEmails: PurchaseEmail[];
  domainRedirectTo: string | null;
}

interface PurchaseEmail {
  first_name: string;
  last_name: string;
  email_account: string;
  password: string;
  profilePicture?: string;
}

const RegistrationConfirmation: React.FC<RegistrationConfirmationProps> = ({
  selectedDomains,
  mailboxes,
  costPerMailbox,
  domainCost,
  redirectUrl,
  isLoading,
  onRegister,
  emailWorkspaceType,
  isZapMailFlow
}) => {

  const [showPricingModal, setShowPricingModal] = useState(false);
  const [billingSubscriptionDetail, setBillingSubscriptionDetails] = useState<Settings.IBillingSubscriptionDetails[]>([]);

  const [additionalAddonsToPurchase, setAdditionalAddonsToPurchase] = useState<IRequiredEmailAndDomains | undefined>(undefined)
  const [polling, setPolling] = useState(false);
  const [loadingBillingApis, setLoadingBillingApis] = useState(false);
  const [hasAcknowlegdedWarmup, setHasAcknowlegdedWarmup] = useState<boolean | undefined>(undefined);
  const pollingInterval = CONSTANTS.EMAIL_INFRA.POLLING_ADDONS_COUNT_FOR_PURCHASING_DOMAINS_AND_EMAILS_INTERVAL;

  console.log('debug selectedDomains : ', selectedDomains, 'mailboxes', mailboxes, 'redirectUrl', redirectUrl ,  'costPerMailbox', costPerMailbox, 'domainCost', domainCost);
  const totalDomainCost = selectedDomains.length * domainCost;
  const totalMailboxCost = mailboxes.reduce((total, mailbox) =>
    total + (mailbox.emailAddresses?.length || 0) * costPerMailbox, 0);
  // const gst = (totalDomainCost + totalMailboxCost) * 0.18; // 18% GST
  if (selectedDomains.length === 0) {
    console.log(`Should never happens [selected domains should not be empty at all] : totalDomainCost: ${totalDomainCost}, totalMailboxCost: ${totalMailboxCost}, totalCost: ${totalDomainCost + totalMailboxCost}`)
  }
  const is_additional_email_flow = selectedDomains.map(domain=> !!domain.uuid)[0]
  const totalCost = is_additional_email_flow ? totalMailboxCost : totalDomainCost + totalMailboxCost;

  const getTotalEmailCount = () => {
    return mailboxes.reduce((total, mailbox) =>
      total + (mailbox.emailAddresses?.length || 0), 0);
  };
  console.log('debug 2');


  const purchaseDomainsAndEmails = () => {
    return onRegister(preparePurchasedDomains(), is_additional_email_flow,hasAcknowlegdedWarmup)
  };

  const preparePurchasedDomains = (): PurchasedDomain[] => {
    console.log('debug mailboxes', mailboxes, selectedDomains, redirectUrl);
    return selectedDomains.map((domain) => {

      /*

       28-Jan-2025:


        SelectedDomains -> coming from 1st page

         export interface ISelectedDomain {
            name: string,
            uuid?: string
          }

        Mailboxes -> coming from 2nd page, all the selected mailboxes.

        export interface Mailbox {
          firstName: string;
          lastName: string;
          emailPrefix: string;
          emailAddresses: EmailAddress[];
        }

        export interface EmailAddress {
          email: string;
          domain: ISRDropdownOption;
        }

      */

      const filteredMailboxes = mailboxes.filter(m => !!m.emailAddresses.filter(em => em.domain.displayText === domain.name).length)

      const purchasedEmails: PurchaseEmail[] = filteredMailboxes.map((mailbox) => ({
        first_name: mailbox.firstName,
        last_name: mailbox.lastName,
        email_account: `${mailbox.emailPrefix}@${domain.name}`,
        profilePicture: mailbox.profilePicture,
        password: "dummy_password",
      }));

      return {
        domainName: domain.name,
        purchasedEmails,
        domainRedirectTo: redirectUrl
      };
    });
  };

  const getAddonsCount = async () => {

    const emailAccountsToPurchase: string[] = mailboxes.flatMap(x => x.emailAddresses).flatMap(x => x.email)
    const domainsToPurchase: string[] = is_additional_email_flow ? [] : selectedDomains.flatMap(x => x.name)

    const res = await getRequiredAddonsCountForDomainsAndEmailsPurchase({
      emails_to_purchase: emailAccountsToPurchase,
      domains_to_purchase: domainsToPurchase,
      platform_type: isZapMailFlow ? 'zapmail' : 'maildoso',
      workspace_type: emailWorkspaceType
    }, logInStore.currentTeamId)

    setAdditionalAddonsToPurchase(res)

  }

  const pollingAddonsCount = async () => {

    const totalEmailAccountsToPurchase = mailboxes.flatMap(x => x.emailAddresses).length
    const totalDomainsToPurchase = selectedDomains.length
    const res = await getAddonsCountForDomainsAndEmailsPurchase(
      totalEmailAccountsToPurchase,
      totalDomainsToPurchase,
      isZapMailFlow? 'zapmail' : 'maildoso')

    setAdditionalAddonsToPurchase(res)

    if (
      res.required_purchased_emails_qty === 0 &&
      res.required_purchased_domains_qty === 0
    ) {
      setPolling(false);
      purchaseDomainsAndEmails();
    }

  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoadingBillingApis(true);

        // Step 3: Get subscription details

        const subscriptionResponse = await billingApi.getBillingSubscriptionDetails();
        // moving all cancelled subscription at end
        const subscriptionsWithSortOrder = subscriptionResponse.data.subscriptions.map(subscription => ({
          ...subscription,
          sortOrder: subscription.subscription_status.status_type === 'scheduled_for_cancellation' ? 1 : 0
        }));

        const sortedSubscriptionData = sortBy(subscriptionsWithSortOrder, ['sortOrder']);

        setBillingSubscriptionDetails(sortedSubscriptionData);

        // Step 4: Get addons count
        await getAddonsCount();

        setLoadingBillingApis(false);

      } catch (error) {
        setLoadingBillingApis(false);
        console.error("Error fetching billing data:", error);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    if (polling) {
      const interval = setInterval(pollingAddonsCount, pollingInterval); // Poll every 5 seconds
      return () => clearInterval(interval);
    } else return;
  }, [polling]);

  useEffect(() => {

    getAddonsCount()

  }, [])

  const toggleHasAcknowlegdedWarmup = () => {
    setHasAcknowlegdedWarmup(prev => prev === undefined ? true : !prev);
  }

  console.log('debug 3');

  const activeSubscription = billingSubscriptionDetail.find(
    (sub) => sub.subscription_status.status_type == "active"
  );

  const isPurchaseEmailsAndDomainsSupportedByPlan =
    logInStore.getAccountInfo.org.org_metadata
      .is_purchase_emails_and_domains_supported_by_plan ?? false;

  const emailsAccountCount = getTotalEmailCount();

  const disablePurchaseButton = isLoading || loadingBillingApis || (emailWorkspaceType === 'MICROSOFT' && !hasAcknowlegdedWarmup)

  return (
    <div className="relative h-full mx-4 mt-4">
      {showPricingModal && billingSubscriptionDetail && (
        <SrModal
          title="Customize your plan with add-ons"
          size="small"
          onClose={() => setShowPricingModal(false)}
          content={
            <div>
              {isPurchaseEmailsAndDomainsSupportedByPlan === false ? (
                <PlanNotSupportedForEmailAndDomainsPurchase />
              ) : activeSubscription === undefined ? (
                <div>
                  It looks like you don't have an active subscription. Please
                  activate a subscription or contact our support team for
                  assistance.
                </div>
              ) : (
                <UpdateSubscriptionAddons
                  closeAndStartPolling={() => {
                    setPolling(true);
                    setShowPricingModal(false);
                  }}
                  activeSubscription={activeSubscription}
                  additionalRequiredAddons={[
                    {
                      addon_license_type: isZapMailFlow
                        ? "purchased-zapmail-domains-addon"
                        : "purchased-domains-addon",
                      additional_required_quantity:
                        additionalAddonsToPurchase?.required_purchased_domains_qty ??
                        0,
                    },
                    {
                      addon_license_type: isZapMailFlow
                        ? "purchased-zapmail-email-accounts-addon"
                        : "purchased-email-accounts-addon",
                      additional_required_quantity:
                        additionalAddonsToPurchase?.required_purchased_emails_qty ??
                        0,
                    },
                  ]}
                />
              )}
            </div>
          }
        />
      )}

      <div className="grid grid-cols-2 gap-8 mt-[24px]">
        {/* Left Column - Preview */}
        <div>
          {/* Domains Preview */}
          {!is_additional_email_flow &&<div className="mb-8">
            <div className='flex flex-col gap-[2px]'>
              <div className="sr-h4">Your Selected Domains</div>
              <span className="sr-p-basic text-sr-gray-90">
                {selectedDomains?.length || 0} {selectedDomains.length === 1 ? 'domain' : 'domains'}
              </span>
            </div>

            <div className="mt-[16px]">
              <div className="bg-sr-gray-10 rounded-md py-[8px] px-[24px] sr-h6">
                Domain Names
              </div>

              {!!selectedDomains?.length && selectedDomains?.length > 0 && (
                <>
                  <div className="overflow-auto max-h-[292px]">
                    {selectedDomains.map((domain, index) => (
                      <div key={index} className="flex items-center justify-between py-[16px] px-[24px] border-b">
                        <div className="flex items-center w-1/2">
                          <span className={classNames("sr-p-basic", "truncate")}>{index + 1}. {domain.name}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}

            </div>
          </div>
          }

          {/* Email Accounts Preview */}
          <div>
            <div className='flex flex-col gap-[2px]'>
              <div className="sr-h4">New Email Accounts</div>
              <span className="sr-p-basic text-sr-gray-90">
              {getTotalEmailCount()} emails accounts across {mailboxes.length} senders
              </span>
            </div>

            <div className="mt-[16px]">
              <div className="bg-sr-gray-10 rounded-md py-[8px] px-[24px] sr-h6 flex gap-[32px]">
                <div className='w-3/5'>Email addresses</div>
                <div className='flex-1'>Sender</div>
              </div>

              {!!selectedDomains?.length && selectedDomains?.length > 0 && (
                <>
                  <div className="overflow-auto max-h-[292px]">
                    {mailboxes.map((mailbox, index) =>
                    mailbox.emailAddresses?.map((emailAddress, index) => (
                        <div className="flex gap-[32px] items-center py-[16px] px-[24px] border-b">
                          <span className={classNames("sr-p-basic", "w-3/5", "truncate")}>{emailAddress.email}</span>
                          <span className={classNames("sr-p-basic", 'flex-1')}>{mailbox.firstName} {mailbox.lastName}</span>
                        </div>
                    )))}
                  </div>
                </>
              )}

            </div>

            {/* <div className="p-4 rounded-md bg-gray-50">
              <div className="grid grid-cols-2 pb-2 mb-2 border-b border-gray-200">
                <span className="text-sm font-medium text-gray-700">Email address</span>
                <span className="text-sm font-medium text-gray-700">Sender</span>
              </div>
              <div className="space-y-2">
                {mailboxes.map((mailbox) =>
                  mailbox.emailAddresses?.map((emailAddress, index) => (
                    <div key={index} className="grid grid-cols-2 py-2">
                      <span className="text-sm text-gray-700">{emailAddress.email}</span>
                      <span className="text-sm text-gray-700">
                        {mailbox.firstName} {mailbox.lastName}
                      </span>
                    </div>
                  ))
                )}
              </div>
            </div> */}
          </div>
        </div>

        {/* Right Column - Price Details */}
        <div className='p-6 bg-sr-gray-10 rounded-2xl'>
          <div className='flex flex-col gap-[2px]'>
            <div className="sr-h4">Price Details</div>
            <span className="sr-p-basic text-sr-gray-90">
              Monthly plan
            </span>
          </div>

          <div className="rounded-md mt-[16px] flex flex-col gap-[12px] sr-p-normal">
            {!is_additional_email_flow && <div className="flex items-center justify-between">
              <span className="text-sr-grey-90">Domains ({selectedDomains.length} x {domainCost})</span>
              <span className="font-semibold">${totalDomainCost}</span>
            </div>
            }

            <div className="flex items-center justify-between">
              <span className="text-sr-grey-90">
                Email Accounts ({getTotalEmailCount()} x {costPerMailbox})
              </span>
              <span className="font-semibold">${totalMailboxCost}</span>
            </div>

            {/* <div className="flex items-center justify-between">
              <span className="text-gray-700">GST (18%)</span>
              <span className="font-medium text-gray-900">${gst.toFixed(2)}</span>
            </div> */}

            <div className="flex items-center justify-between pt-4 border-t border-gray-200 sr-h4">
              <span className="">Total Price</span>
              <span className="">${totalCost.toFixed(2)}</span>
            </div>

            <div className='flex gap-2 px-4 py-2 mt-4 mb-2 bg-sr-light-blue text-sr-dark-blue rounded-xl'>
              <SrIconHealthCheck />
              <span className='font-semibold'>Secure Payment:</span>
              <span>All transactions are encrypted and secure</span>
            </div>

            {isZapMailFlow &&
            <div className='px-4 py-2 mt-2 mb-2 bg-sr-lightest-yellow text-sr-warning-80 rounded-xl border border-sr-warning-50'>
              <div className='font-semibold'>⏱️ Setup Time:</div>
              <div>Email account setup may take up to 24 hours to complete after purchase.</div>
            </div>}

            {emailWorkspaceType === 'MICROSOFT' &&
            <div className='flex items-center gap-2'>
                <SRCheckbox
                  displayText="Note that warmup is not available for Microsoft 365 email account"
                  checked={hasAcknowlegdedWarmup}
                  onClick={() => toggleHasAcknowlegdedWarmup()}
                />
            </div>}
            <div className="flex space-x-4">

              {additionalAddonsToPurchase === undefined ?
                <div className='flex items-center justify-center w-full h-full'><SRSpinner /></div> :

                ((additionalAddonsToPurchase.required_purchased_domains_qty > 0 || additionalAddonsToPurchase.required_purchased_emails_qty > 0) ?

                <div onClick={() =>{
                   setShowPricingModal(true)
                   }} className='!w-full py-[8px]'>
                  <SRButtonFilled
                    className="sr-p-basic"
                    text={`Purchase ${additionalAddonsToPurchase.required_purchased_domains_qty} domain addons and ${additionalAddonsToPurchase.required_purchased_emails_qty} email addons`}
                    width='fluid'
                    isPrimary
                    loading={polling}
                    disable={disablePurchaseButton}
                  />
                </div> :

                <SRButtonFilled
                  onClick={purchaseDomainsAndEmails}
                  text={`Complete Purchase & Activate Your ${(!is_additional_email_flow && emailsAccountCount > 0) ? "Domains and Emails": !is_additional_email_flow ? "Domains" : "Emails" }`}
                  className='!text-base'
                  width='fluid'
                  isGreen={true}
                  loading={isLoading}
                  disable={disablePurchaseButton}
                />
                )}

            </div>

            {

              isZapMailFlow ?
                <div className='sr-p-small text-sr-grey-90 text-left mt-[12px]'>
                  By purchasing these Domain(s) and Email(s), you agree to Zapmails's{' '}
                  <a href="https://zapmail.ai/Terms-&-Conditions" target='_blank' className="text-blue-600 hover:underline">Terms of Service</a>
                  , as they provide the domains and email accounts.
                </div>
                : <div className='sr-p-small text-sr-grey-90 text-left mt-[12px]'>
              By purchasing these Domain(s) and Email(s), you agree to Maildoso's{' '}
              <a href="https://getmaildoso.com/terms-of-use" target='_blank' className="text-blue-600 hover:underline">Terms of Service</a>
              , as they provide the domains and email accounts.
            </div>
            }

            {/* <div className="sr-p-small text-sr-grey-90 text-left mt-[12px]">
              You can purchase the addon credits for domains and emails using this link
              and then come back here to complete the purchase: <br /><br/>
              <SRLink className='text-sr-primary-80 hover:text-sr-primary-80 hover:underline flex items-center gap-[4px]' to='/dashboard/billing_settings/goto_billing' target='_blank'>
                Purchase email and domain addons <SrIconExternalIcon className='!h-[18px] !w-[18px]' />
              </SRLink>
            </div> */}

          </div>
        </div>
      </div>
    </div>
  );
};

export default RegistrationConfirmation;
