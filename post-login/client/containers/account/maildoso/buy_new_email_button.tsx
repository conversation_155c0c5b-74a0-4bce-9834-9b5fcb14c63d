import React from "react";
import { SRB<PERSON>onOutline, SRTooltip2, classNames } from "@sr/design-component";
import { ISelectedDomain } from "./buy_new_domains";
import { logInStore } from "../../../stores/LogInStore";
import { checkBillingPermission } from "../../../utils/permissions_related";
import { PlanBasedModalWrapper } from "../../billing/client/components/billing_v2/plan-based-modal-wrapper";
import PaymentRequiredModal from "../../../components/payment-due-modal";
import { crispShowChatBox } from "../../../utils/crisp";

interface BuyNewEmailButtonProps {
  domain: ISelectedDomain;
  onClick: (domain: ISelectedDomain) => void;
}

export const BuyNewEmailButton: React.FC<BuyNewEmailButtonProps> = ({
  domain,
  onClick,
}) => {
  const baseClassName =
    "text-sr-success-80 !font-semibold !border !border-sr-success-80 !px-4 !py-[6px]";

  const permissions = logInStore.getTeamRolePermissions.permissions;
  const account = logInStore.getAccountInfo;
  const orgMetadata = account.org.org_metadata;
  const planInfo = account.org.plan;

  const showPurchasedDomainsAndEmails =
    orgMetadata.show_purchased_domains_and_emails ?? false;

  const isPurchaseSupportedByPlan =
    orgMetadata.is_purchase_emails_and_domains_supported_by_plan_version ??
    false;

  const invoiceUrl = planInfo?.payment_due_invoice_link;
  const paymentDueCampaignPauseAt = planInfo?.payment_due_campaign_pause_at;

  const isAgencyAdmin =
    account.account_type === "agency" &&
    logInStore.roleIsOrgOwnerOrAgencyAdminForAgency;
  const canManageBilling = checkBillingPermission(
    permissions.manage_billing,
    isAgencyAdmin
  );

  // Only the user who has billing access can buy domains and emails.
  // Further in the process we have to make a billing API call,
  // which will fail if the user does not have billing access.
  const canBuy = canManageBilling;

  const [showBillingModal, setShowBillingModal] = React.useState(false);

  const [showPaymentRequiredModal, setShowPaymentRequiredModal] =
    React.useState(false);

  const handleUpgradeClick = () => {
    if (isPurchaseSupportedByPlan) {
      setShowBillingModal(true);
    } else {
      crispShowChatBox();
    }
  };

  const handleBuyClick = () => {
    if (invoiceUrl && paymentDueCampaignPauseAt) {
      setShowPaymentRequiredModal(true);
    } else {
      onClick(domain);
    }
  };

  // Check if this is a Zapmail domain
  const isZapmailGoogleDomain =
    domain.platformType === "zapmail" && domain.workspaceType === "GOOGLE";

  // If the user does not have billing permission
  // then don't allow them to do anything.
  if (!canBuy) {
    return (
      <SRTooltip2
        text={"Please ask your admin to buy addons"}
        direction="top center"
      >
        <div>
          <SRButtonOutline
            className={classNames(
              baseClassName,
              "opacity-60 cursor-not-allowed"
            )}
            text="Buy new email"
            disable={true}
          />
        </div>
      </SRTooltip2>
    );
  }

  // If this is a Zapmail domain, disable the button with the delay message
  if (isZapmailGoogleDomain) {
    return (
      <SRTooltip2
        text="Our Google Workspace mailbox setup is currently undergoing maintenance and will be available again next week. Thank you for your patience!"
        direction="top center"
      >
        <div>
          <SRButtonOutline
            className={classNames(
              baseClassName,
              "opacity-60 cursor-not-allowed"
            )}
            text="Buy new email"
            disable={true}
          />
        </div>
      </SRTooltip2>
    );
  }

  return (
    <>
      {showPurchasedDomainsAndEmails ? (
        <SRButtonOutline
          onClick={handleBuyClick}
          className={classNames(baseClassName)}
          isGreen={true}
          text="Buy new email"
        />
      ) : (
        <SRButtonOutline
          onClick={handleUpgradeClick}
          text={
            isPurchaseSupportedByPlan
              ? "Upgrade to plus plan"
              : "Contact support to buy emails"
          }
          isGreen={true}
        />
      )}

      {showBillingModal && (
        <PlanBasedModalWrapper
          onClose={() => setShowBillingModal(false)}
          shouldReloadPageOnSuccess={true}
          shouldDisableLowestBasePlans={true}
        />
      )}

      {showPaymentRequiredModal && paymentDueCampaignPauseAt && invoiceUrl && (
        <PaymentRequiredModal
          invoiceUrl={invoiceUrl}
          paymentDueCampaignPauseAt={paymentDueCampaignPauseAt}
          onClose={() => setShowPaymentRequiredModal(false)}
        />
      )}
    </>
  );
};
