import { getProspectColChoice } from '../../utils/prospectTableColumnLocalStore';
import * as _ from 'lodash';
import { removeUnderscoresAndCapitalise } from '../../utils/remove-underscores';
//import { dateFormat } from '../../utils/date';
// import { labelTextColor } from '../../utils/colorUtils';
import * as React from 'react';
import { SRLink } from '../helpers';
import { setProspectColChoice } from '../../utils/prospectTableColumnLocalStore';
import { HLDataGrid } from '../data-grid/data-grid';
import * as moment from 'moment';
import { CONSTANTS } from '../../data/constants';
import { SRLabel, SRLabel3, SRPopover } from '@sr/design-component';
import { SRButtonOutline } from '@sr/design-component';
import { SrIconIssues, SrIconTickCircle,SrIconOutlineCircle,SRAvatar } from '@sr/design-component';
import { logInStore } from '../../stores/LogInStore';
import { columnDateFormat } from '../../utils/timezone-utils';
import { SRTooltip2 } from '@sr/design-component';
import { LogIn, Prospects, Campaigns, getTitle } from '@sr/shared-product-components';
import { getLinkedInText } from '../../utils/prospect-title-utils';
import ReplySentimentComponent from '../reply-sentiment';
// import { SrIconIssues } from '@sr/design-component';
import * as teamsApi from '../../api/teams';
import { ColumnState } from 'ag-grid-community';
import { checkIfEmailStepTypeExistFromStepTypes } from '../../utils/campaign-utils';
import { getDisplayStepType } from '../../utils/campaignSetupData';

type IAllowedStatusLabelColors = 'green' | 'blue' | 'grey' | 'yellow' | 'red' | 'orange' | 'black' | 'white' | 'primary';
type ICellRendererParam = HLDataGrid.IAGGridCellRendererParam<Prospects.IProspectSearchResult>;

function emailLinkFormatterGenerator(data: {prospectView: Prospects.IProspectView,index?:number, campaignSteps?: Campaigns.ICampaignStepType[]}) {

  return function emailLinkFormatter(params: ICellRendererParam) {
    const internal = params.data.internal;

    const textInPopUp = (internal.invalid_email != undefined)
      ? internal.invalid_email
        ? 'Email failed validation'
        : 'Email passed validation'
      : 'Email will be validated once a campaign is started for this prospect'

    const basePath = window.location.pathname;//preserves params
    const openInNewTab = (
      data.prospectView !== 'global_prospects_page'
    );

    const currentCampaign = _.find(internal.active_campaigns, (campaign) => {
      return (campaign.campaign_id === internal.current_campaign_id);
    })

    // Check if there are email step types in the campaign
    const hasEmailSteps = checkIfEmailStepTypeExistFromStepTypes(data.campaignSteps);

    const showEmailVerificationStatus = data.prospectView != 'campaign_failed_prospects_tab' && hasEmailSteps;

    return (
      <div className='flex items-center max-w-[300px]'>
        {!!params.value && showEmailVerificationStatus &&
          <SRTooltip2 text={<div >
            {textInPopUp}
            <a target='_blank' className='underline ml-1' href='https://help.smartreach.io/docs/email-validation'>Know More</a>
            {/* <a target='_blank' href='https://support.smartreach.io/frequently-asked-questions/how-does-email-validation-work-in-smartreachio'><i className='info circle icon'></i></a> */}
          </div>}>

            <div className={`h-6 w-6 mr-1
              ${(internal.invalid_email != undefined)
                ? (internal.invalid_email
                  ? 'text-sr-default-red'
                  : 'text-sr-default-green'
                ) : 'text-sr-default-yellow'
              }
           `}>
              {
                (internal.invalid_email != undefined)
                  ? (internal.invalid_email
                    ? <SrIconIssues />
                    : <SrIconTickCircle />
                  ) : <SrIconOutlineCircle />
              }
            </div>
            </SRTooltip2>
        }
        {!!currentCampaign?.will_resume_at &&
          <SRTooltip2 text={`Scheduled to start from ${moment(currentCampaign.will_resume_at).format('MMM DD, YYYY')} in ${currentCampaign.will_resume_at_tz} timezone`}>
          <i className={'clock icon'}></i>
          </SRTooltip2>

        }

        {!!currentCampaign?.current_holiday_name &&
          <SRTooltip2 text={`Sending to this prospect is paused temporarily because there is a holiday now: '${currentCampaign.current_holiday_name}' from calendar '${currentCampaign.sending_holiday_calendar_name}'`} >
            <i className={'info orange icon'}></i>
          </SRTooltip2>
        }

      <div >
        {openInNewTab
          ?
              <SRLink className= 'prospect-datagrid-external-link-tw text-sr-gray-100 hover:text-sr-default-blue hover:underline' target='_blank' to={`/dashboard/prospects/${params.data.id}/activity`}>
              <div className='w-full flex hover:underline'>
                <div>{params.value}</div>

              </div>
            </SRLink>

            : <SRLink className='text-sr-gray-100 hover:text-sr-default-blue hover:underline' target='_blank' to={`${basePath}/${params.data.id}/activity`}>{params.value}</SRLink>
        }
        </div>

        {internal.flags?.will_delete &&
          <i className="red flag icon" title="Prospect queued for deleting"></i>}
      </div >
    );
  }

}


function formatDate(params: ICellRendererParam) {
  // console.log('formatDate', params.value)
  const timeStamp = params.value;
  const timezone = logInStore.accountInfo.timezone
  // const formattedDate = dateFormat(timeStamp);
  // return (
  //   <div title={formattedDate}>{formattedDate}</div>
  // );
  if (!!timeStamp) {

    //const formattedDate = dateFormat(timeStamp);
    const formattedDate = columnDateFormat(timeStamp, timezone || 'UTC')

    return (<div>{formattedDate}</div>);

  } else {

    return <div></div>;
  }
}


function missingMergeTagRenderer(params: ICellRendererParam) {

  const c = params.data.internal.active_campaigns
  return (
    <div style={{ color: 'red' }}>{c?.length
      ? c[0].to_check_fields?.join(', ')
      : ''
    }</div>
  );
}


function lastRepliedAtFormatter(params: ICellRendererParam) {
  const timeStamp = params.data.internal.last_replied_at;
  const timezone = logInStore.accountInfo.timezone
  if (!!timeStamp) {
    const formattedDate = columnDateFormat(timeStamp, timezone || 'UTC')
    return (<div>{formattedDate}</div>);
  } else {
    return <div></div>;
  }
}

function lastOpenedAtFormatter(params: ICellRendererParam) {
  const timeStamp = params.data.internal.last_opened_at;

  if (!!timeStamp) {
    const timezone = logInStore.accountInfo.timezone
    const formattedDate = columnDateFormat(timeStamp, timezone || 'UTC')
    return (<div>{formattedDate}</div>);
  } else {
    return <div></div>;
  }
}

function lastCallMadeAtFormatter(params: ICellRendererParam) {
  const timeStamp = params.data.internal.last_call_made_at;

  if (!!timeStamp) {
    const timezone = logInStore.accountInfo.timezone
    const formattedDate = columnDateFormat(timeStamp, timezone || 'UTC')
    return (<div>{formattedDate}</div>);
  } else {
    return <div></div>;
  }
}

function customFieldFormatter(params: ICellRendererParam) {
  const columnKey = params.colDef.field;
  const columnValue = params.data.custom_fields[columnKey];

  return (
    <div title={columnKey}>{columnValue}</div>
  );
}


const contactInfoFormatter = (params: ICellRendererParam, emailLinkFormatter: (params: ICellRendererParam) => any) => {
  const email = params.data.email;
  const phone = params.data.phone;

  const secondaryPhonesLength = !!params.data.phone_2 ? !!params.data.phone_3 ? 2: 1 : 0

  if (!email && !phone) {
    return (<div>No contact info available</div>); // Handle empty case
  }

  const emailFormatted = emailLinkFormatter({ ...params, value: email });

  const phoneData = <>
    {phone}{!!secondaryPhonesLength ? `, +${secondaryPhonesLength} more` : ""}
  </>

  return (
    <div className="">
      <div className='sr-p-basic'>{emailFormatted}</div>
      <div className='sr-p-basic pl-[24px]'>{phoneData}</div>
    </div>
  );
};


function prospectAndCategoryFormatter(params: ICellRendererParam) {
  // const firstName = params.data.first_name;
  // const lastName = params.data.last_name;
  const category = params.data.prospect_category;

  const prospectTitle = getTitle(params.data);

  const categoryFormatted = prospectCategoryFormatter({ ...params, value: category });

  if (prospectTitle === "" && !category) {
    return (<div>No data available</div>); // Handle empty case
  }

  return (
    <div>
      <div className='sr-p-basic'>{prospectTitle || '---------'}</div>
      <div>{categoryFormatted}</div>
    </div>
  );
}

export function getProspectStatusPopover(params: ICellRendererParam, campaignSteps?: Campaigns.ICampaignStepType[]): string | undefined {
  const internal = params.data.internal
  const currentCampaignId = internal.current_campaign_id;
  const sts = _.find(internal.active_campaigns, ac => ac.campaign_id === currentCampaignId);
  const prospectCategory = params.data.prospect_category;

  if (!sts) {
    return undefined;
  }

  const { popover } = determineUnifiedProspectStatus(
    sts,
    prospectCategory,
    campaignSteps
  )

  return popover
}

function currentStepAndStatusFormatter(params:ICellRendererParam, campaignSteps?: Campaigns.ICampaignStepType[], campaign_type?: Campaigns.campaign_type) {
  const currentStep = currentEmailStepFormatter({...params}, campaign_type)
  const currentStatus = currentProspectStatusFormatter(params, campaignSteps)
  return (
    <div className="flex gap-2">
        <div className='flex items-center justify-center'>{currentStep}</div>
        <div>{currentStatus}</div>
    </div>
  )
}


// function getProspectCategoryColor(colorString:string) {
//   const colors = ['bg-[#2ba02d]',
//     'bg-[#97df89]',
//     'bg-[#ff9897]',
//     'bg-[#c4b0d5]',
//     'bg-[#ffbc78]',
//     'bg-[#f7b7d2]',
//     'bg-[#d52728]',
//     'bg-[#c7c7c7]',
//     'bg-[#fe7f0e]']
//     return _.find(colors,(color)=> _.includes(color,colorString))
// }


function prospectCategoryFormatter(params: ICellRendererParam) {
  const internal = params.data.internal;
  // const color = getProspectCategoryColor(internal.prospect_category_label_color)
  const labelColor = internal.prospect_category_label_color
  return (
    <div>
      {!!params.value &&
        <div className='flex items-center gap-[4px]'>
          <div
            className={`h-2 w-2 rounded-full`}
            style={{ backgroundColor: labelColor }}
          ></div>
          <span className='sr-p-small'>{(params.value as string)}</span>
          {/* <Label style={{
            fontWeight: 'normal',
            backgroundColor: internal.prospect_category_label_color,
            color: labelTextColor(internal.prospect_category_label_color)
          }} size='mini'>{(params.value as string).toUpperCase()}</Label> */}
        </div>
      }

    </div >
  );
}

function currentEmailStepFormatter(params: ICellRendererParam, campaign_type?: Campaigns.campaign_type) {
  const c = params.data.internal.active_campaigns;

  if(campaign_type != 'drip'){
    return (
      <div className='sr-p-basic'>{c && c.length && c[0].step ? c[0].step : ''}</div>
    );
  } else {
    return (
      <div className='sr-p-basic'>{c && c.length && !!c[0].display_node_id ? `Step ${c[0].display_node_id}` : c[0].step ? c[0].step : ''}</div>
    );
  }





}

function latestReplySentimentFormatter(params: ICellRendererParam, replySentimentsForTeam?:ReplySentiments.IReplySentimentTypeForTeam[] ) {
  const c = params.data.latest_reply_sentiment_uuid;


    return (
    <div>
      <ReplySentimentComponent
        selectedReplySentimentUuid={c}
        replySentimentsForTeam={replySentimentsForTeam}
      />
    </div>
    )


}

function campaignNameFormatter(params: ICellRendererParam) {
  let campaignsName = '';
  const c = params.data.internal.active_campaigns

  {
    _.map(c, (campaign, index) => {
      campaignsName = campaignsName + (index ? ', ' : '') + campaign.campaign_name;
    })
  }
  return (
    <div title={campaignsName}>
      {campaignsName}
    </div>
  );
}

function currentProspectStatusFormatter(params: ICellRendererParam, campaignSteps?: Campaigns.ICampaignStepType[]) {
  const internal = params.data.internal
  const currentCampaignId = internal.current_campaign_id;
  const sts = _.find(internal.active_campaigns, ac => ac.campaign_id === currentCampaignId);
  const prospectCategory = params.data.prospect_category;

  return !sts
    ? null
    : prospectStatusFormatter(
      sts,
      prospectCategory,
      campaignSteps
    )

}

function currentSenderEmailFormatter(params: ICellRendererParam) {
  const internal = params.data.internal
  const currentCampaignId = internal.current_campaign_id;
  const sts = _.find(internal.active_campaigns, ac => ac.campaign_id === currentCampaignId);
  const sender_email = sts?.sender_email;

  return !sender_email
    ? null
    : sender_email

}


function forceSentEarlierFormatter(params: ICellRendererParam) {
  // console.log('force send', props, this.state.markingAsValidId !== 0);
  const forceSentEarlier = params.data.internal.flags?.force_send_invalid_email;
  return forceSentEarlier
    ? <span><span className={'bg-sr-danger-60 inline-block h-2 w-2 mr-2 flex-shrink-0 rounded-full'}/>Yes</span>
    : <span><span className={'bg-sr-gray-60 inline-block h-2 w-2 mr-2 flex-shrink-0 rounded-full'}/>No</span>
}

function hardBounceFormatter(params: ICellRendererParam) {
  // console.log('force send', props, this.state.markingAsValidId !== 0);
  const hardBounced = params.data.internal.flags?.email_bounced
  return hardBounced
    ? <span><span className={'bg-sr-danger-60 inline-block h-2 w-2 mr-2 flex-shrink-0 rounded-full'}/>Yes</span>
    : <span><span className={'bg-sr-gray-60 inline-block h-2 w-2 mr-2 flex-shrink-0 rounded-full'}/>No</span>
}

function prospectAccountLinkFormatter(params: ICellRendererParam) {
  const internal = params.data.internal;

  return (
    <div className='flex'>
      <SRLink className='prospect-datagrid-external-link-tw'
        target='_blank'
        to={`/dashboard/accounts/${internal.prospect_account_uuid}/activity`}>
        <div className='w-full flex hover:underline text-sr-default-blue hover:text-sr-default-blue'>
          <div>{internal.prospect_account}</div>

          </div>
      </SRLink>
    </div>
  )
}

function ownerFormatter(params: ICellRendererParam) {
  const internal = params.data.internal

  return <SRAvatar name={internal.owner_name}/>
}

function tagsFormatter(params: ICellRendererParam) {
  const tags = params.data.internal.tags || [];
  return (
    <div>
      {
        _.map(tags, (tag) => <SRLabel className='text-center' text={_.upperCase(tag.tag)} color='grey' size='small'/>)
      }
    </div>
  );
}


export function getDataGridColumns(data: {
  prospectView: Prospects.IProspectView,
  columnDefs: ColumnDef.IColumnDefServer[],
  forceSendFormatter: (params: ICellRendererParam) => any,
  activeItem?: Prospects.ICPErrorTabs,
  account: LogIn.IAccount,
  campaignSteps?: Campaigns.ICampaignStepType[],
  replySentimentsForTeam?: ReplySentiments.IReplySentimentTypeForTeam[],

}): HLDataGrid.IDataGridColumn[] {


  const {
    prospectView,
    columnDefs,
    forceSendFormatter,
    activeItem

  } = data;


  // BASE COLUMNS

  const lockedColumn: HLDataGrid.IDataGridColumn= {
    field: " ",
    sortable: false,
    field_type: 'text',
    headerName: '',
    is_custom: false,
    width: 50,
    minWidth: 50,
    locked: true,
    allowSelection: prospectView !== 'campaign_failed_prospects_tab',
    cellRenderer: (params: ICellRendererParam) => { return <div className='z-5 hover:cursor-pointer !max-w-[50px]'></div> }
  };

  const baseDGColumns: HLDataGrid.IDataGridColumn[] = _.concat([lockedColumn], _.map(columnDefs)
    .map((column,index) => {

      const headerName = column.is_custom
        ? removeUnderscoresAndCapitalise(column.display_name)
        : column.display_name
        ;

      const colDataGrid: HLDataGrid.IDataGridColumn = _.cloneDeep({
        field: column.name,
        sortable: column.sortable,
        field_type: column.field_type,
        headerName: headerName,
        is_custom: column.is_custom,
        width: 120,
        hide: false, // NOTE: this will be set later in this function by checking localstorage
        minWidth: 120
      });


      if (column.is_custom) {
        colDataGrid.cellRenderer = customFieldFormatter;

        if (column.field_type === 'magic') {
          colDataGrid.width = 250
        }

      } else {
        if (column.name === 'email') {
          colDataGrid.cellRenderer = emailLinkFormatterGenerator({
            prospectView: prospectView,
            index : index,
            campaignSteps: data.campaignSteps
          });
          colDataGrid.width = 300;
          // colDataGrid.locked = true;
          // colDataGrid.allowSelection = prospectView !== 'campaign_failed_prospects_tab';
          colDataGrid.minWidth = 300;

        }

        if (column.name === 'owner_name') {
          colDataGrid.headerName = 'Owner';
          colDataGrid.cellRenderer = ownerFormatter;
        }

        if (column.name === 'step') {
          colDataGrid.cellRenderer = currentEmailStepFormatter;
          colDataGrid.width = 200;
        }

        if (column.name === 'prospect_category') { // FIXME CHECK PC
          colDataGrid.cellRenderer = prospectCategoryFormatter;
          colDataGrid.headerName = 'Category';
        }

        if (column.name === 'created_at') {
          colDataGrid.cellRenderer = formatDate;
        }
        if (column.name === 'last_contacted_at') {
          colDataGrid.cellRenderer = formatDate;
        }
        if (column.name === 'latest_task_done_at'){
          colDataGrid.cellRenderer = formatDate;
        }
        if (column.name === 'last_replied_at') {
          colDataGrid.cellRenderer = lastRepliedAtFormatter;
        }
        if (column.name === 'last_opened_at') {
          colDataGrid.cellRenderer = lastOpenedAtFormatter;
        }

        if (column.name === 'prospect_account') {
          colDataGrid.cellRenderer = prospectAccountLinkFormatter;
          colDataGrid.width = 200;
        }

        if (column.name === 'tags') {
          colDataGrid.cellRenderer = tagsFormatter;
          colDataGrid.width = 250;
        }

        if (column.name === 'total_opens') {
          colDataGrid.cellRenderer = (params: ICellRendererParam) => { return params.data.internal.total_opens };
        }

        if (column.name === 'total_clicks') {
          colDataGrid.cellRenderer = (params: ICellRendererParam) => { return params.data.internal.total_clicks };
        }

        if (column.name === 'prospect_source') {
          colDataGrid.cellRenderer = (params: ICellRendererParam) => { return params.data.internal.prospect_source };
        }
        if(column.name == 'current_sender_email') {
          colDataGrid.cellRenderer =  currentSenderEmailFormatter
        }
        if (column.name === 'latest_reply_sentiment') {
          colDataGrid.cellRenderer = (param)=>latestReplySentimentFormatter(param,data.replySentimentsForTeam),
            colDataGrid.width = 250
        }

        if (column.name === 'linkedin_url') {
          colDataGrid.cellRenderer = (params: ICellRendererParam) => {
            const res = getLinkedInText(params.data.linkedin_url || "");
            return (
              <div>
                {params.data.linkedin_url &&
                  <a href={params.data.linkedin_url} target = '_blank' className='text-sr-default-blue hover:underline hover:text-sr-default-blue'>
                    {res}
                  </a>
                }
              </div>
            );
          }
        }

      }
      return colDataGrid;
    }))


    const reorderedColumns = reorderProspectColumns(
      baseDGColumns,
      prospectView,
      data.account.org.id,
      forceSendFormatter,
      data.campaignSteps
    )


  const storedCol = getProspectColChoice();

  const colDefDataGrid: HLDataGrid.IDataGridColumn[] = _.chain(reorderedColumns)
    .filter(c => !_.isEmpty(c))
    .map((column) => {
      let hide = false;

      if (storedCol && storedCol.length > 0) {
        hide = _.find(storedCol, c => c.name === column.field)?.hide || false;
      }

      const colDataGrid: HLDataGrid.IDataGridColumn = {
        ...column,
        hide: hide
      };

      return colDataGrid;
    })
    .map(c => {
      if (
        prospectView === 'campaign_failed_prospects_tab' &&
        (
          (
            (activeItem === 'to_check_fields' || activeItem === 'to_check') &&
            !_.includes(['email', 'to_check_fields'], c.field)
          )
          ||
          (
            activeItem === 'failed_validation' &&
            !_.includes(['email', 'first_name', 'last_name',
              'force_send', 'email_bounced', 'force_send_invalid_email'], c.field)
          )
        )
      ) {
        const res: HLDataGrid.IDataGridColumn = {
          ...c,
          hide: true
        };
        return res;
      } else { return c }
    }) //TODO need cross verify why only created_at is filtering out
    // .filter((column) => {

    //   return (column.field === 'created_at');

    // })
    .value();
  return colDefDataGrid;
}

/*
export function createEmptyRow() {
  let item: Prospects.IProspect[] = [];
  let obj: Prospects.IProspect = { custom_fields: {} as any, flags: { will_delete: false }, active_campaigns: [] as Prospects.IProspectActiveCampaign[] } as Prospects.IProspect;
  item.push(obj);
  return item;
}
*/

/*
export function prospectSentStatusString(channelType?: Campaigns.ICampaignStepType) {
  return channelType == "call" ? //in backend we are updating sent=true if call task is done
  'Call:PLACED' : channelType == "send_email" || channelType == "manual_send_email" ?
   "Email: SENT" : "CONTACTED"
}
*/

function getStepPopoverMessage(
  current_step_type: Campaigns.ICampaignStepType,
  scheduler_status?: Campaigns.ISchedulerStatus,
): string {
  switch (current_step_type) {
    case "manual_send_email":
    case "manual_email_magic_content":
      return "Manual email needs to be sent to the prospect.";

    case "send_linkedin_connection_request":
      return "Send a LinkedIn connection request manually.";

    case "send_linkedin_message":
      return "Send a LinkedIn message manually.";

    case "send_linkedin_inmail":
      return "Send a LinkedIn InMail manually.";

    case "linkedin_view_profile":
      return "View the prospect's LinkedIn profile.";

    case "general_task":
      return "A manual task is assigned for this prospect.";

    case "send_whatsapp_message":
      return "Send a WhatsApp message manually.";

    case "send_sms":
      return "Send an SMS message manually.";

    case "call":
      return "Call the prospect.";

    case "move_to_another_campaign":
      return "Move this prospect to another campaign.";

    case "auto_send_linkedin_connection_request":
      return "An automated LinkedIn connection request will be sent.";

    case "auto_send_linkedin_message":
      return "An automated LinkedIn message will be sent.";

    case "auto_send_linkedin_inmail":
      return "An automated LinkedIn InMail will be sent.";

    case "auto_linkedin_view_profile":
      return "The prospect's LinkedIn profile will be automatically viewed.";

    case "auto_email_magic_content":
      switch (scheduler_status) {
        case "pending_approval":
          return "Approval required to send email";
        case "ai_content_queued":
          return "Magic content task is getting generated"
        case "done":
          return "Email sent"
        default:
          return "Email will be sent to the prospect.";
      }
    case "send_email":
      switch (scheduler_status) {
        case "done":
          return "Email has been sent to the prospect."
        default:
          return "Email will be sent to the prospect.";
      }
  }
}

function getStatusForMagicContent(
  scheduler_status?: Campaigns.ISchedulerStatus
) {
  if(!!scheduler_status){
    switch (scheduler_status) {
      case "pending_approval":
        return "Pending approval";
      case "ai_content_queued":
        return "AI content generation"
      case "done":
        return "Done"
      case "approved":
        return "Approved"
      default:
        return "Active";
    }
  } else {
    return "Active"
  }

}


export function determineUnifiedProspectStatus(
  sts: Prospects.IProspectActiveCampaign,
  prospectCategory: string,
  campaignSteps?: Campaigns.ICampaignStepType[]
): { status: string; color: IAllowedStatusLabelColors; popover?: string } {
  const isDNC =
    prospectCategory === "do_not_contact" ||
    prospectCategory.toLowerCase() === "do not contact";

  if (isDNC) {
    return {
      status: "Stopped (DNC)",
      color: "red",
      popover: "This prospect is on the Do Not Contact list.",
    };
  }

  // Check if there are email step types in the campaign
  const hasEmailSteps = checkIfEmailStepTypeExistFromStepTypes(campaignSteps);

  if ((sts.bounced || sts.invalid_email || sts.to_check) && hasEmailSteps) {
    const popover = sts.bounced
      ? "The email sent to the prospect has bounced."
      : sts.invalid_email
      ? "The prospect has an invalid email address."
      : sts.to_check
      ? `Required fields are missing: ${sts.to_check_fields?.join(", ")}.`
      : undefined;

    return { status: "Issue", color: "red", popover: popover };
  }

  // if replied and completed then it should say as Replied
  if (sts.replied) {
    return { status: "Replied", color: "green", popover: undefined };
  }

  if (sts.out_of_office || sts.auto_reply || sts.opted_out) {
    const popover = sts.out_of_office
      ? "The prospect is currently out of office."
      : sts.auto_reply
      ? "An automatic reply was received from the prospect."
      : sts.opted_out
      ? "The prospect has unsubscribed from communications."
      : undefined;

    return { status: "Paused", color: "orange", popover: popover };
  }

  // if completed and not replied then it should say as Completed
  if (sts.completed) {
    return { status: "Completed", color: "blue", popover: undefined };
  }

  const current_step_type = sts.current_step_type;

  if (current_step_type) {
    return (() => {
      switch (current_step_type) {
        case "manual_send_email":
        case "manual_email_magic_content":
        case "send_linkedin_connection_request":
        case "send_linkedin_message":
        case "send_linkedin_inmail":
        case "linkedin_view_profile":
        case "general_task":
        case "send_whatsapp_message":
        case "send_sms":
        case "call":
        case "move_to_another_campaign":
          return {
            status: getDisplayStepType(current_step_type),
            color: "green",
            popover: getStepPopoverMessage(current_step_type, sts.scheduler_status),
          };

        case "auto_send_linkedin_connection_request":
        case "auto_send_linkedin_message":
        case "auto_send_linkedin_inmail":
        case "auto_linkedin_view_profile":
        case "send_email":
          return {
            status: getDisplayStepType(current_step_type),
            color: "green",
            popover: getStepPopoverMessage(current_step_type, sts.scheduler_status),
          };

        case "auto_email_magic_content":
          return {
            status: getStatusForMagicContent(sts.scheduler_status),
            color: "green",
            popover: getStepPopoverMessage(current_step_type, sts.scheduler_status),
          }

      }
    })();
  }

  return { status: "Not Started", color: "grey", popover: undefined }; // TODO: fix color
}

export function prospectStatusFormatter(
  prospectStatusInfo: Prospects.IProspectActiveCampaign,
  prospectCategory: string,
  campaignSteps?: Campaigns.ICampaignStepType[]
) {
  const { status, color } = determineUnifiedProspectStatus(
    prospectStatusInfo,
    prospectCategory,
    campaignSteps
  );

  return <SRLabel3 className="text-center" text={status} color={color} />;
}

/*
export function prospectStatusFormatterX(
  prospectStatusInfo: Prospects.IProspectActiveCampaign,
  prospectCategory: string
) {
  const sts = prospectStatusInfo;

  var primaryStatus: string | null = null;
  var primaryStatusColor: IAllowedStatusLabelColors | null = null;

  if (sts != undefined) {

    var completedStatus: string | null = null;
    var completedStatusColor: IAllowedStatusLabelColors | null = null;

    var errorStatus: string | null = null;

    var sentStatus: string | null = null;
    var sentStatusColor: IAllowedStatusLabelColors | null = null;

    const isDNC = prospectCategory === 'do_not_contact' ||
      prospectCategory.toLowerCase() === "do not contact";

    if (sts.bounced) {

      completedStatus = 'BOUNCED'
      completedStatusColor = 'red'

    } else if (sts.replied && sts.replied_marked_by_adminid) {

      completedStatus = 'REPLIED MARKED BY ADMIN'
      completedStatusColor = 'green'

    } else if (sts.replied) {

      completedStatus = 'REPLIED'
      completedStatusColor = 'green'

    } else if (sts.auto_reply) {

      completedStatus = 'AUTO REPLIED'
      completedStatusColor = 'orange'

    } else if (sts.out_of_office) {

      completedStatus = 'OUT OF OFFICE'
      completedStatusColor = 'orange'

    } else if (sts.opted_out) {

      completedStatus = 'UNSUBSCRIBED'
      completedStatusColor = 'orange'

    } else if (sts.completed) {

      completedStatus = sts.replied ? 'COMPLETED' : 'COMPLETED: NO REPLY';
      completedStatusColor = 'green'

    }

    if (isDNC) {
      errorStatus = 'DO NOT CONTACT'
    } else if (sts.to_check) {
      errorStatus = 'MISSING MERGE TAGS'
    } else if (sts.invalid_email && !sts.bounced) {
      errorStatus = 'INVALID EMAIL'
    }

    if (sts.clicked) {
      sentStatus = 'CLICKED';
      sentStatusColor = 'green';
    } else if (sts.opened && !sts.invalid_email) {
      sentStatus = 'OPENED'
      sentStatusColor = 'green'
    } else if (sts.sent) {

      const sentStatusForChannel = prospectSentStatusString(sts.current_step_type)

      sentStatus = sentStatusForChannel
      sentStatusColor = 'green'
    }


    if (errorStatus) {

      if (completedStatus) {
        primaryStatus = completedStatus;
        primaryStatusColor = completedStatusColor;
      } else {
        primaryStatus = sentStatus;
        primaryStatusColor = sentStatusColor;
      }

    } else {

      primaryStatus = sentStatus;
      primaryStatusColor = sentStatusColor;

    }



  }

  return (
    <div className='w-full sr-p-small'>
      {primaryStatus && primaryStatusColor &&

        // <SRLabel className='text-center' text = {primaryStatus} color = {primaryStatusColor }/>
        <div>{primaryStatus}</div>

      }
      {(!primaryStatus && ((sts != undefined) ? !sts.sent : false)) &&
        // <SRLabel className ='text-center'  text = {'Not Started'} color = {'grey'}/>
        <div>Not Started</div>
      }
    </div>
  );
}
*/


export class ShowHideColumn extends React.Component<
  {
    popupButtondisabled: boolean,
    checkboxDisabled: boolean,
    dgColumns: HLDataGrid.IDataGridColumn[],
    onSelectionChange: (checkBoxDisabled: boolean) => any
    // onClickEdit?:()=>void
    onClickAddNewColumn?: () => void
    onClickMagicColumn?: ()=> void
  },
  {}
> {

  columnChecked(index: number, e: any) {
    const columns = this.props.dgColumns.filter(p => p.headerName !== '') || [];
    if (columns.length !== 0) {
      columns[index].hide = !e.target.checked;
    }
    const len = _.filter(columns || [], (obj) => { return !obj.hide; }).length;


    let storedata = _.map(columns, (col) => {
      return { name: col.field, hide: col.hide || false, is_custom: col.is_custom };
    });
    setProspectColChoice(storedata);

    this.props.onSelectionChange(len < 4);
  }


  render() {
    const columns = this.props.dgColumns.filter( p=> p.headerName !=='')
    return <SRPopover
      triggerElement={

        <SRButtonOutline
          icon='sr_icon_show_content'
          iconPosition='left'
          text='Show columns'
          disable={this.props.popupButtondisabled}
          style = {{borderColor:'#DBDFE5'}}
          className={' text-sr-default-grey sr-h6'}
        />
      }
      className='!z-20'
      direction='bottom-right'
      style={{backgroundColor: '#FFF'}}
    >

      {(logInStore.getAccountInfo.org.org_metadata.enable_magic_column)&&<div className='whitespace-nowrap'>
        <SRButtonOutline
          text='Add Magic Column'
          onClick={this.props.onClickMagicColumn}
          width='fluid'
          icon='sr_icon_enhance'
          className='bg-white my-1 !text-sr-indigo-60'
        />
      </div>}

      <SRButtonOutline
        text='Add New Column'
        icon='sr_icon_add'
        width='fluid'
        iconPosition='left'
        onClick={this.props.onClickAddNewColumn}
        className='bg-white'
      />
      <div className='overflow-y-scroll h-[300px] w-fit scroll-smooth'>
        {_.map(columns, (col, index) => {
          if (col.field !== 'email' && col.field !== 'first_name' && col.field !== 'last_name' && col.field !== 'prospect_category' && col.field !== 'phone' && col.field !== 'step') {
            return (
              <div key={col.field} className='my-1 h-[40px] py-1 w-[200px] flex  whitespace-nowrap cursor-default hover:rounded hover:bg-sr-light-blue '>
                <label className='my-auto cursor-pointer'>
                  <input type='checkbox'
                    className=' rounded ml-2 align-middle'
                    checked={!col.hide}
                    disabled={this.props.checkboxDisabled && !col.hide}
                    onChange={this.columnChecked.bind(this, index)} />
                  <span className="mx-3  sr-h6" style={{ fontWeight: 400 }}>{col.headerName}</span>
                </label>
                {/* <span className='absolute right-1 '>
                    <SRButtonOutline
                      text='Edit'
                      isPrimary
                      onClick={this.props.onClickEdit}
                      className='bg-white sr-h6 ml-auto px-6  items-end'
                    />
                  </span> */}

              </div>);
          } else { return; }
        })}
      </div>
    </SRPopover>

  }
}


export function reorderProspectColumns(
  columns: HLDataGrid.IDataGridColumn[],
  prospectView: Prospects.IProspectView,
  orgId: number,
  forceSendFormatter: (params: ICellRendererParam) => any,
  campaignSteps?: Campaigns.ICampaignStepType[]
) {

  const reorderedColumns = columns

  const colDataGrid: HLDataGrid.IDataGridColumn = {
    field: " ",
    sortable: false,
    field_type: 'text',
    headerName: '',
    is_custom: false,
    width: 50,
    minWidth: 50,
    locked: true,
    allowSelection: false,
    cellRenderer: (params: ICellRendererParam) => { return <div></div> }
  };

  const index = _.findIndex(reorderedColumns, rc => rc.field === 'contact_info') + 1

  const linkedinCol = _.find(columns, rc => rc.field === 'linkedin_url') || colDataGrid
  const indexOflinkCol = _.findIndex(reorderedColumns, rc => rc.field === 'linkedin_url');

  // Remove the column at the found index
  if (indexOflinkCol !== -1) {
    reorderedColumns.splice(indexOflinkCol, 1);
  }

  const companyCol = _.find(columns, rc => rc.field === 'company') || colDataGrid
  const indexOfCompanyCol = _.findIndex(reorderedColumns, rc => rc.field === 'company');

  // Remove the column at the found index
  if (indexOfCompanyCol !== -1) {
    reorderedColumns.splice(indexOfCompanyCol, 1);
  }

  /**
   * moved company and linkedin columns after contact info
   */

  if(reorderedColumns.length > index) {
    reorderedColumns.splice(index, 0, companyCol);
    reorderedColumns.splice(index + 1, 0, linkedinCol);
  }


  // CUSTOM PAGE SPECIFIC COLUMNS
  if (prospectView === 'global_prospects_page') {

    const campaignColumn: HLDataGrid.IDataGridColumn = {
      field: 'campaign_name',
      headerName: 'Active campaigns',
      field_type: 'text',
      is_custom: false,

      //for multi campaign
      cellRenderer: campaignNameFormatter,
      width: 280,
      minWidth: 280,
    };

    /* We are inserting columns for specific pages at 'index' index, as first_name and last_name should come together
      the index is +1 the index of 'last_name' column.
    */
    if(reorderedColumns.length > index) {
      reorderedColumns.splice(index + 2, 0, campaignColumn);
    }

  } else if (prospectView === 'campaign_prospect_page') {

    // const currentStatusCol: HLDataGrid.IDataGridColumn = {
    //   field: 'current_prospect_status',
    //   headerName: 'Status in campaign',
    //   field_type: 'text',
    //   is_custom: false,

    //   cellRenderer: currentProspectStatusFormatter,
    //   width: 180,

    // };

    // const currentSenderEmailCol: HLDataGrid.IDataGridColumn = {
    //   field: 'sender_email',
    //   headerName: 'Current Sender Email',
    //   field_type: 'text',
    //   is_custom: false,

    //   cellRenderer: currentSenderEmailFormatter,
    //   width: 280,

    // };

    // const indexOfLastName: number = _.findIndex(reorderedColumns, rc => rc.field === 'prospect_and_category')

    /*
      The campaign status column should be placed at 2nd position but if in that position
      'last_name' column lies, we put the campaign status column at the 3rd position.
    */
    // const indexForCampaignStatus = indexOfLastName === 2 ? 3 : 2

    // if(reorderedColumns.length > indexForCampaignStatus)
    //   reorderedColumns.splice(indexForCampaignStatus, 0, currentStatusCol);
    // baseDGColumns.splice(4, 0, currentSenderEmailCol);


    const showOpensClicksInCampaign = _.includes(
      CONSTANTS.SHOW_TOTAL_OPENS_CLICKS_IN_CAMPAIGN_FOR_ORG_IDS,
      orgId
    );

    if (showOpensClicksInCampaign) {

      const totalOpensInCampaign: HLDataGrid.IDataGridColumn = {
        field: 'total_opens_in_campaign',
        headerName: 'Total opens in campaign',
        field_type: 'number',
        is_custom: false,

        cellRenderer: (params: ICellRendererParam) => {
          return params.data.internal.active_campaigns[0].total_opens_in_campaign
        },

        width: 280,

      };

      const totalClicksInCampaign: HLDataGrid.IDataGridColumn = {
        field: 'total_clicks_in_campaign',
        headerName: 'Total clicks in campaign',
        field_type: 'number',
        is_custom: false,

        cellRenderer: (params: ICellRendererParam) => {
          return params.data.internal.active_campaigns[0].total_clicks_in_campaign
        },

        width: 280,

      };

      reorderedColumns.push(
        totalOpensInCampaign,
        totalClicksInCampaign
      );

    }

  } else if (prospectView === 'campaign_failed_prospects_tab') {

    const toCheckFieldsColumn: HLDataGrid.IDataGridColumn = {
      field: 'to_check_fields',
      headerName: 'Missing fields',
      field_type: 'text',
      is_custom: false,

      cellRenderer: missingMergeTagRenderer,
      width: 500,
    };
    reorderedColumns.push(toCheckFieldsColumn);

    const hardBouncedCol: HLDataGrid.IDataGridColumn = {
      field: 'email_bounced',
      headerName: 'Hard bounced',
      field_type: 'text',
      is_custom: false,

      cellRenderer: hardBounceFormatter,
      width: 200,
      // minWidth: 200,

    };
    reorderedColumns.push(hardBouncedCol);

    const forceSentCol: HLDataGrid.IDataGridColumn = {
      field: 'force_send_invalid_email',
      headerName: 'Force sent earlier',
      field_type: 'text',
      is_custom: false,

      cellRenderer: forceSentEarlierFormatter,
      width: 200,

    };
    reorderedColumns.push(forceSentCol);

    const forceSendBtnCol: HLDataGrid.IDataGridColumn = {
      field: 'force_send',
      headerName: '',
      field_type: 'text',
      is_custom: false,

      cellRenderer: forceSendFormatter,
      width: 200,
      minWidth: 250,

    };
    reorderedColumns.push(forceSendBtnCol);
  }

  const indexForProspectCategory = _.findIndex(reorderedColumns, rc => rc.field === 'last_name') + 1

  // without the below check, when baseDGColumns was empty, it was making with [undefined]
  if (reorderedColumns.length > indexForProspectCategory) {
    // MOVE prospect_category after 'last_name' column.
    const indexOfProspectCategory = _.findIndex(reorderedColumns, c => c.field === 'prospect_category');
    reorderedColumns.splice(indexForProspectCategory, 0, reorderedColumns.splice(indexOfProspectCategory, 1)[0])

    // MOVE prospect_category after 'last_name' column only inside campaigns_prospects,
    // changing globally causes the column to appear in places other than campaigns_prospects
    if(prospectView === 'campaign_prospect_page' || prospectView === 'campaign_failed_prospects_tab') {
      const currSenderEmailCol = reorderedColumns.splice(_.findIndex(reorderedColumns, rc => rc.field === 'current_sender_email'), 1)[0]
      currSenderEmailCol.headerName = "Sending email"
      currSenderEmailCol.width = 180
      reorderedColumns.splice(indexForProspectCategory, 0, currSenderEmailCol)
    }
  }

  return reorderedColumns

}



export async function getDataGridColumnsNew(data: {
  prospectView: Prospects.IProspectView,
  columnDefs: ColumnDef.IColumnDefServer[],
  forceSendFormatter: (params: ICellRendererParam) => any,
  activeItem?: Prospects.ICPErrorTabs,
  account: LogIn.IAccount,
  campaignSteps?: Campaigns.ICampaignStepType[],
  campaign_type?: Campaigns.campaign_type,
  replySentimentsForTeam?: ReplySentiments.IReplySentimentTypeForTeam[],

}): Promise<HLDataGrid.IDataGridColumn[]> {

  const {
    prospectView,
    columnDefs,
    forceSendFormatter,
    activeItem

  } = data;


  // BASE COLUMNS

  const lockedColumn: HLDataGrid.IDataGridColumn= {
    field: " ",
    sortable: false,
    field_type: 'text',
    headerName: '',
    is_custom: false,
    width: 50,
    minWidth: 50,
    locked: true,
    pinned:"left",
    lockPinned: true, // Critical fix
    lockPosition: 'left', // New property
    suppressMovable: true,
    allowSelection: prospectView !== 'campaign_failed_prospects_tab',
    cellRenderer: (params: ICellRendererParam) => { return <div className='z-5 hover:cursor-pointer !max-w-[50px]'></div> }
  };

  const createCombinedColumnWithEmailAndPhone = (emailField:ColumnDef.IColumnDefServer , phoneField: ColumnDef.IColumnDefServer):HLDataGrid.IDataGridColumn => {

    const emailLinkFormatter = emailLinkFormatterGenerator({
      prospectView: prospectView,
      campaignSteps: data.campaignSteps
    });

    return {
      headerName: 'Contact Info',
      field: 'contact_info',
      sortable: false,
      field_type: 'text',
      width: 300,
      minWidth: 300,
      is_custom: false,
      cellRenderer: (params) => {
        return contactInfoFormatter(params, emailLinkFormatter);
      }
      }
    };



    // Function to create combined column for first name, last name, and category
  const createCombinedColumnWithProspectAndCategory = (
    firstNameField: ColumnDef.IColumnDefServer,
    lastNameField: ColumnDef.IColumnDefServer,
    categoryField: ColumnDef.IColumnDefServer
  ): HLDataGrid.IDataGridColumn => {

    return {
      headerName: 'Prospect and Category',
      field: 'prospect_and_category',
      sortable: false,
      field_type: 'text',
      width: 200,
      minWidth: 150,
      locked: true,
      pinned:"left",
      lockPinned: true, // Critical fix
      lockPosition: 'left', // New property
      suppressMovable: true,
      is_custom: false,
      cellRenderer: prospectAndCategoryFormatter
    };
  };

  const createCurrentStepAndCampaignStatus = (campaignSteps?: Campaigns.ICampaignStepType[], campaign_type?: Campaigns.campaign_type) : HLDataGrid.IDataGridColumn => {
    return {
      headerName: 'Current step and status',
      field: 'current_step_and_status',
      sortable: false,
      field_type: 'text',
      width: 300,
      minWidth: 150,
      is_custom: false,
      cellRenderer : (params: ICellRendererParam) => currentStepAndStatusFormatter(params, campaignSteps, campaign_type),
      tooltipValueGetter: (params: ICellRendererParam) => getProspectStatusPopover(params, campaignSteps),
    };
  }



    const emailColumnDef = columnDefs.find(col => col.name === 'email');
    const phoneColumnDef = columnDefs.find(col => col.name === 'phone');

  const firstNameColumnDef = columnDefs.find(col => col.name === 'first_name');
  const lastNameColumnDef = columnDefs.find(col => col.name === 'last_name');
  const categoryColumnDef = columnDefs.find(col => col.name === 'prospect_category');

  // Create combined columns if all required fields are found
  let combinedColumnsArray = [];

  if (firstNameColumnDef && lastNameColumnDef && categoryColumnDef) {
    combinedColumnsArray.push(createCombinedColumnWithProspectAndCategory(firstNameColumnDef, lastNameColumnDef, categoryColumnDef));
  }

  if(prospectView === 'campaign_prospect_page') {
    combinedColumnsArray.push(createCurrentStepAndCampaignStatus(data.campaignSteps, data.campaign_type));
  }

  if (emailColumnDef && phoneColumnDef) {
    combinedColumnsArray.push(createCombinedColumnWithEmailAndPhone(emailColumnDef, phoneColumnDef));
  }

    // // Create combined column if both email and phone columns are found
    // let combinedColumn: HLDataGrid.IDataGridColumn | undefined;
    // if (emailColumnDef && phoneColumnDef) {
    //   combinedColumn = createCombinedColumnWithEmailAndPhone(emailColumnDef, phoneColumnDef);
    // }






  const baseDGColumns: HLDataGrid.IDataGridColumn[] = _.concat( _.map(columnDefs)
    .map((column,index) => {

      const headerName = column.is_custom
        ? removeUnderscoresAndCapitalise(column.display_name)
        : column.display_name
        ;

      const colDataGrid: HLDataGrid.IDataGridColumn = _.cloneDeep({
        field: column.name,
        id: column.id,
        sortable: column.sortable,
        field_type: column.field_type,
        headerName: headerName,
        is_custom: column.is_custom,
        width: 120,
        hide: false, // NOTE: this will be set later in this function by checking localstorage
        minWidth: 120
      });


      if (column.is_custom) {
        colDataGrid.cellRenderer = customFieldFormatter;

        if (column.field_type === 'magic') {
          colDataGrid.width = 250
        }

      } else {
        if (column.name === 'email' || column.name === 'phone') {
          colDataGrid.width = 250
          colDataGrid.hide = prospectView !== 'campaign_failed_prospects_tab'; // Hide these columns to avoid duplication
        }


        if (column.name === 'owner_name') {
          colDataGrid.headerName = 'Owner';
          colDataGrid.cellRenderer = ownerFormatter;
        }

        if (column.name === 'step') {
          // colDataGrid.cellRenderer = currentEmailStepFormatter;
          // colDataGrid.width = 200;
          colDataGrid.hide = true;
        }

        if (column.name === 'prospect_category') { // FIXME CHECK PC
          colDataGrid.hide = true;
        }

        if (column.name === 'created_at') {
          colDataGrid.cellRenderer = formatDate;
        }
        if (column.name === 'last_contacted_at') {
          colDataGrid.cellRenderer = formatDate;
        }
        if (column.name === 'latest_task_done_at'){
          colDataGrid.cellRenderer = formatDate;
        }
        if (column.name === 'last_replied_at') {
          colDataGrid.cellRenderer = lastRepliedAtFormatter;
        }
        if (column.name === 'last_opened_at') {
          colDataGrid.cellRenderer = lastOpenedAtFormatter;
        }

        if(column.name === 'last_call_made_at'){
          colDataGrid.cellRenderer = lastCallMadeAtFormatter;
        }

        if (column.name === 'prospect_account') {
          colDataGrid.cellRenderer = prospectAccountLinkFormatter;
          colDataGrid.width = 200;
        }

        if (column.name === 'tags') {
          colDataGrid.cellRenderer = tagsFormatter;
          colDataGrid.width = 250;
        }

        if (column.name === 'total_opens') {
          colDataGrid.cellRenderer = (params: ICellRendererParam) => { return params.data.internal.total_opens };
        }

        if (column.name === 'total_clicks') {
          colDataGrid.cellRenderer = (params: ICellRendererParam) => { return params.data.internal.total_clicks };
        }

        if (column.name === 'prospect_source') {
          colDataGrid.cellRenderer = (params: ICellRendererParam) => { return params.data.internal.prospect_source };
        }
        if(column.name == 'current_sender_email') {
          colDataGrid.cellRenderer =  currentSenderEmailFormatter
        }
        if (column.name === 'latest_reply_sentiment') {
          colDataGrid.cellRenderer = (param)=>latestReplySentimentFormatter(param,data.replySentimentsForTeam),
            colDataGrid.width = 250
        }

        if (column.name === 'linkedin_url') {
          colDataGrid.cellRenderer = (params: ICellRendererParam) => {
            const res = getLinkedInText(params.data.linkedin_url || "");
            return (
              <div>
                {params.data.linkedin_url &&
                  <a href={params.data.linkedin_url} target = '_blank' className='text-sr-default-blue hover:underline hover:text-sr-default-blue'>
                    {res}
                  </a>
                }
              </div>
            );
          }
        }

      }
      return colDataGrid;
    }))


  const finalColumns = [lockedColumn,...combinedColumnsArray, ...baseDGColumns]

  // const savedState = JSON.parse(
  //   localStorage.getItem(`agGridColumnState_${prospectView}`) || '[]'
  // );

  const getSavedColumnState = async (teamId: number, isGlobalPage: boolean) => {
  if (prospectView === 'global_prospects_page' || prospectView === 'campaign_prospect_page') {
    try {
      const response = await teamsApi.getProspectColumnOrder(teamId, isGlobalPage);
      const rawData = response.data.column_order || '[]';
      const parsed = JSON.parse(rawData);
      const colOrderArr: ColumnState[] = parsed.column_order;
      return Array.isArray(colOrderArr) ? colOrderArr : [];
    } catch (error) {
      console.error('Error loading column state:', error);
      return [];
    }
  }
  return [];
  };

  const savedState = await getSavedColumnState(
    logInStore.getCurrentTeamId,
    prospectView === 'global_prospects_page'
  );

  console.log("savedState",savedState)

  const currentFields = new Set(finalColumns.map(c => c.field));
  const validSavedState = savedState.filter((s: { colId: string }) => currentFields.has(s.colId));

  const mergedColumns = finalColumns.sort((a, b) => {

  if (a.lockPosition === 'left' && b.lockPosition !== 'left') return -1;
  if (b.lockPosition === 'left' && a.lockPosition !== 'left') return 1;

  const aIndex = validSavedState.findIndex((s: { colId: string }) => s.colId === a.field);
  const bIndex = validSavedState.findIndex((s: { colId: string }) => s.colId === b.field);

  if (aIndex > -1 && bIndex > -1) return aIndex - bIndex;
  if (aIndex > -1) return -1;
  if (bIndex > -1) return 1;

  return finalColumns.indexOf(a) - finalColumns.indexOf(b);
  });

  console.log("merged columns", mergedColumns)


    const reorderedColumns = reorderProspectColumns(
      // baseDGColumns,
      // finalColumns,
      mergedColumns,
      prospectView,
      data.account.org.id,
      forceSendFormatter,
      data.campaignSteps
    )

  const storedCol = getProspectColChoice();

  const colDefDataGrid: HLDataGrid.IDataGridColumn[] = _.chain(reorderedColumns)
    .filter(c => !_.isEmpty(c))
    .map((column) => {
      let hide = false;

      if (storedCol && storedCol.length > 0) {
        hide = _.find(storedCol, c => c.name === column.field)?.hide || false;
      }

      if (prospectView === 'campaign_failed_prospects_tab' && column.field === 'email') {
        hide = false;

    }

      if ((prospectView != 'campaign_failed_prospects_tab' && column.field === 'email') || column.field === 'phone' || (prospectView!= 'campaign_failed_prospects_tab' && column.field === 'first_name') || (prospectView != 'campaign_failed_prospects_tab' && column.field === 'last_name') || column.field === 'prospect_category' || column.field === 'step') {
        hide = true;
      }

      const colDataGrid: HLDataGrid.IDataGridColumn = {
        ...column,
        hide: hide
      };

      return colDataGrid;
    })
    .map(c => {
      if (
        prospectView === 'campaign_failed_prospects_tab' &&
        (
          (
            (activeItem === 'to_check_fields' || activeItem === 'to_check') &&
            !_.includes(['email', 'to_check_fields'], c.field)
          )
          ||
          (
            activeItem === 'failed_validation' &&
            !_.includes(['email', 'first_name', 'last_name',
              'force_send', 'email_bounced', 'force_send_invalid_email'], c.field)
          )
        )
      ) {
        const res: HLDataGrid.IDataGridColumn = {
          ...c,
          hide: true
        };
        return res;
      } else { return c }
    }) //TODO need cross verify why only created_at is filtering out
    // .filter((column) => {

    //   return (column.field === 'created_at');

    // })
    .value();
    console.log("colDefDataGrid", colDefDataGrid)
  return colDefDataGrid;
}