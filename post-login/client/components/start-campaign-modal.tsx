import * as React from 'react';
import * as campaignsApi from '../api/campaigns';
import { inject, observer } from 'mobx-react';
import { RouteComponentProps, withRouter } from 'react-router-dom';
import DatePicker from 'react-datepicker';
import * as settingsApi from '../api/settings';
import * as _ from 'lodash';
import * as moment from 'moment-timezone';
import { campaignStore } from '../stores/CampaignStore';
import { ISRDropdownOption, SRButtonOutline, SRSimpleSelectionDropdown, SrModal, SRButtonFilled, SRIconArrowRight, SrIconExternalIcon } from '@sr/design-component';
import { SRMessageBox } from '@sr/design-component';
import { Formik, Field, Form } from 'formik';
import { LogIn, Campaigns } from '@sr/shared-product-components';
import SendingModeSelection from '../containers/campaign/sending-mode';
import { goToNextStep as goToNextStepUtil, goToPreviousStep as goToPreviousStepUtil, isNextDisabled as isNextDisabledUtil, getModalTitle, shouldShowPreviousButton, StartCampaignStep, isAutomaticStepExists, hasAnyManualSteps } from './start-campaign-modal-utils';

interface IStartCampaignModalProps extends RouteComponentProps<any> {
  campaignStore?: Campaigns.ICampaignStore;
  logInStore?: LogIn.ILogInStore;
  alertStore?: Alerts.IAlertStore;
  onClose?: () => void;
  canEditSetting: boolean;

  defaultTimezone: string;
  campaignId: number;
  reloadCampaignsApi?: (isScheduled: boolean, campaignId: number, warm_up: boolean) => void;
  fromAllCampaignsPage?: boolean;
  isCampaignPaused: boolean;
}

interface IDropdownOption {
  key: any;
  value: string;
  text: string;
}

interface IStartCampaignModalStates {
  startCampaignStep: StartCampaignStep;
  startCampaignNowOrLater?: 'now' | 'later' | undefined;
  campaignStartDate?: Date;
  isFetchingTimezones?: boolean;
  timezoneOptions: IDropdownOption[];
  selectedTimezone?: string;
  isStartingCampaign?: boolean;
  startCampaignWithOrWithoutWarmup?: 'with_warm_up' | 'without_warm_up' | undefined;
  warmUpLengthInDays: number;
  warmUpStartingEmailCount: number;
  isUsingCalendarLinkMergeTag: boolean;
  show_soft_start_settings: boolean;
  sendingMode?: 'CoPilot' | 'AutoPilot' | undefined;
  hasOnlyManualSteps: boolean;
  hasAnyManualSteps: boolean;
  campaignStarted: boolean;
  campaignName: string;
}

class StartCampaignModalComponent extends React.Component<
  IStartCampaignModalProps,
  IStartCampaignModalStates
> {
  constructor(props: IStartCampaignModalProps) {
    super(props);
    this.state = {
      isStartingCampaign: false,
      startCampaignStep: this.isMagicContentCampaign() ? StartCampaignStep.SendingModeSelection : StartCampaignStep.ScheduleSettings,
      startCampaignNowOrLater: undefined,

      // allow scheduling from next day onwards
      campaignStartDate: new Date(Date.now() + 3600 * 1000 * 24),

      isFetchingTimezones: true,
      timezoneOptions: [],
      selectedTimezone: this.props.defaultTimezone,
      startCampaignWithOrWithoutWarmup: undefined,
      warmUpLengthInDays: 10,
      warmUpStartingEmailCount: 5,
      isUsingCalendarLinkMergeTag: false,
      show_soft_start_settings: true,
      sendingMode: undefined,
      hasOnlyManualSteps: false,
      hasAnyManualSteps: false,
      campaignStarted: false,
      campaignName: ''
    };

    this.startWithWarmUp = this.startWithWarmUp.bind(this);
    this.startWithoutWamUp = this.startWithoutWamUp.bind(this);
    this.handleCampaignStartScheduleChange =
      this.handleCampaignStartScheduleChange.bind(this);
    this.handleCampaignStartDateChange =
      this.handleCampaignStartDateChange.bind(this);
    this.handleTimezoneChange = this.handleTimezoneChange.bind(this);
    this.goToNextStep = this.goToNextStep.bind(this);
    this.goToPreviousStep = this.goToPreviousStep.bind(this);
    this.isNextDisabled = this.isNextDisabled.bind(this);
    this.handleWarmUpSettingChange = this.handleWarmUpSettingChange.bind(this);
    this.handleWarmupLengthInDaysChange =
      this.handleWarmupLengthInDaysChange.bind(this);
    this.handleWarmUpStartingEmailCountChange =
      this.handleWarmUpStartingEmailCountChange.bind(this);
    this.validateWarmupSettings = this.validateWarmupSettings.bind(this);
    this.finalStepMessageContent = this.finalStepMessageContent.bind(this);
    this.handleSendingModeChange = this.handleSendingModeChange.bind(this);
    this.isMagicContentCampaign = this.isMagicContentCampaign.bind(this);
    this.handleSecondLastStepNext = this.handleSecondLastStepNext.bind(this);
  }

  goToNextStep() {
    goToNextStepUtil({
      currentStep: this.state.startCampaignStep,
      showSoftStartSettings: this.state.show_soft_start_settings,
      hasOnlyManualSteps: this.state.hasOnlyManualSteps,
      onStepChange: (newStep: StartCampaignStep) => {
        this.setState({ startCampaignStep: newStep });
      }
    });
  }

  goToPreviousStep() {
    goToPreviousStepUtil({
      currentStep: this.state.startCampaignStep,
      showSoftStartSettings: this.state.show_soft_start_settings,
      hasOnlyManualSteps: this.state.hasOnlyManualSteps,
      isMagicContent: this.isMagicContentCampaign(),
      onStepChange: (newStep: StartCampaignStep) => {
        this.setState({ startCampaignStep: newStep });
      }
    });
  }

  isNextDisabled() {
    return isNextDisabledUtil({
      currentStep: this.state.startCampaignStep,
      sendingMode: this.state.sendingMode,
      startCampaignNowOrLater: this.state.startCampaignNowOrLater,
      campaignStartDate: this.state.campaignStartDate,
      selectedTimezone: this.state.selectedTimezone,
      startCampaignWithOrWithoutWarmup: this.state.startCampaignWithOrWithoutWarmup
    });
  }

  isStartDisabled() {
    const show_soft_start_setting = this.state.show_soft_start_settings
    const isMagicContent = this.isMagicContentCampaign();

    if(show_soft_start_setting){
      if (!this.state.startCampaignWithOrWithoutWarmup) {
        return true;
      } else {
        return (
          !this.state.warmUpLengthInDays || !this.state.warmUpStartingEmailCount
        );
      }
    }

    // For magic content campaigns, ensure sending mode is selected
    if (isMagicContent && !this.state.sendingMode) {
      return true;
    }

    return false
  }

  startWithWarmUp(
    warmUp_length_in_days: number,
    warmUp_starting_email_count: number
  ) {
    return campaignsApi
      .startWarmup(
        this.props.campaignId,
        warmUp_length_in_days,
        warmUp_starting_email_count
      )
      .then((response) => {
        return this.startCampaignApi()
          .catch(() => {
            campaignStore.setShowBanner(true);
            throw new Error("Failed to start campaign");
          });
      })
      .catch(() => {
        campaignStore.setShowBanner(true);
        throw new Error("Failed to start warmup");
      });
  }

  onClose() {
    this.props.onClose?.();
    if(!!this.props.fromAllCampaignsPage){
      this.props.reloadCampaignsApi!(false, this.props.campaignId, this.state.startCampaignWithOrWithoutWarmup === "with_warm_up");
    }else{
    this.props.history.push({
        pathname: "/dashboard/campaigns",
        search: this.props.location.search, // retain aid, tid
      });
    }
  }

  startCampaignApi() {
    const campaignId = this.props.campaignId;
    // const warmup_is_on =
    //   this.state.startCampaignWithOrWithoutWarmup == "with_warm_up";

    // Update sending mode for magic content campaigns
    const updateSendingModePromise = this.isMagicContentCampaign() && this.state.sendingMode
      ? (() => {
          // Get existing additional settings from campaign store
          const campaignBasicInfo = this.props.campaignStore?.getBasicInfo;
          const settings = campaignBasicInfo?.settings;

          const data: any = {
            email_priority: settings?.email_priority,
            max_emails_per_day: settings?.max_emails_per_day,
            mark_completed_after_days: settings?.mark_completed_after_days,
            open_tracking_enabled: settings?.open_tracking_enabled,
            click_tracking_enabled: settings?.click_tracking_enabled,
            send_plain_text_email: settings?.send_plain_text_email,
            sending_mode: this.state.sendingMode
          };

          if (settings?.enable_email_validation !== undefined) {
            data['enable_email_validation'] = settings.enable_email_validation;
          }

          return campaignsApi.updateAdditionalSettings(campaignId, data);
        })()
      : Promise.resolve();

    return updateSendingModePromise.then(() => {
      if (this.state.startCampaignNowOrLater === "now") {
        return campaignsApi.startCampaign(campaignId)
        // .then(() => {
          // if (!!this.props.fromAllCampaignsPage) {
          //   // this.props.reloadCampaignsApi!(false, campaignId, warmup_is_on);
          // } else {
          //   // this.props.history.push({
          //   //   pathname: "/dashboard/campaigns",
          //   //   search: this.props.location.search, // retain aid, tid
          //   // });
          // }
        // });
      } else {
        const fromTemp = moment(this.state.campaignStartDate)
          .tz(this.state.selectedTimezone!)
          .startOf("day");
        const from = fromTemp.add(1, "hour").toDate().getTime();
        return campaignsApi
          .startCampaign(campaignId, from, this.state.selectedTimezone)
          // .then(() => {
          //   if (!!this.props.fromAllCampaignsPage) {
          //     // this.props.reloadCampaignsApi!(true, campaignId, warmup_is_on);
          //   } else {
          //     // this.props.history.push({
          //     //   pathname: "/dashboard/campaigns",
          //     //   search: this.props.location.search, // retain aid, tid
          //     // });
          //   }
          // });
      }
    });
  }

  startWithoutWamUp() {
    return this.startCampaignApi()
      .then(() => {
        const hideStopWarmUpSuccess = true;
        return campaignsApi
          .stopWarmup(this.props.campaignId, hideStopWarmUpSuccess)
          // .then(() => {
          //   // Don't close modal or navigate here anymore
          //   // this.props.onClose?.();
          // })
          .catch(() => {
            campaignStore.setShowBanner(true);
            throw new Error("Failed to stop warmup");
          });
      })
      .catch(() => {
        campaignStore.setShowBanner(true);
        throw new Error("Failed to start campaign");
      });
  }

  handleCampaignStartScheduleChange(e: any, data: any) {
    this.setState({ startCampaignNowOrLater: data.value });
  }

  handleCampaignStartDateChange(date: any) {
    this.setState({ campaignStartDate: date });
  }

  handleTimezoneChange(e: any, data: any) {
    this.setState({ selectedTimezone: data.value });
  }

  handleWarmUpSettingChange(e: any, data: any) {
    this.setState({ startCampaignWithOrWithoutWarmup: data.value });
  }

  handleWarmupLengthInDaysChange(e: any, data: any) {
    this.setState({ warmUpLengthInDays: parseInt(data.value, 10) });
  }

  handleWarmUpStartingEmailCountChange(e: any, data: any) {
    this.setState({ warmUpStartingEmailCount: parseInt(data.value) });
  }

  fetchTimezones() {
    settingsApi
      .getTimeZone()
      .then((res) => {
        let timeZonesOptions: IDropdownOption[] = [];
        _.forEach(res.data.timezones, (zone) => {
          timeZonesOptions.push({
            key: zone.value,
            text: zone.name,
            value: zone.value,
          });
        });
        this.setState({
          timezoneOptions: timeZonesOptions,
          isFetchingTimezones: false,
        });
      })
      .catch(() => {
        this.setState({
          isFetchingTimezones: false,
        });
      });
  }

  componentDidMount() {
    // Fetch both campaign details and campaign steps
    Promise.all([
      campaignsApi.getCampaignById(this.props.campaignId),
      campaignsApi.getCampaignSteps(this.props.campaignId)
    ])
      .then(([campaignResponse, stepsResponse]) => {
        console.log("campaignResponse :: ", campaignResponse.data.campaign);
        console.log("stepsResponse :: ", stepsResponse.data.campaign_steps);

        // Use the steps from the API response instead of campaign store
        const campaignSteps = stepsResponse.data.campaign_steps || [];
        const isAutomaticStepExist = isAutomaticStepExists(campaignSteps);
        const anyManualSteps = hasAnyManualSteps(campaignSteps);
        const onlyManualSteps = !isAutomaticStepExist && anyManualSteps;
        const show_soft_start_setting = campaignResponse.data.campaign.settings.show_soft_start_setting && isAutomaticStepExist;
        const existingSendingMode = campaignResponse.data.campaign.settings.sending_mode;
        const campaignName = campaignResponse.data.campaign.name;

        this.setState({
          show_soft_start_settings: show_soft_start_setting,
          sendingMode: existingSendingMode,
          hasOnlyManualSteps: onlyManualSteps,
          hasAnyManualSteps: anyManualSteps,
          campaignName: campaignName
        })

        this.fetchTimezones();

      })
      .catch((err) => {
        console.log('debug error while fetching campaign details :', err);
        this.setState({isFetchingTimezones : false})
      })

    const calendar_link_text = "{{calendar_link}}";
    let usingCalendar = false;
    this.props.campaignStore?.getContentTabInfo.stepVariants.forEach((step) => {
      step.variants?.forEach((variant) => {
        if ("body" in variant.step_data && variant.step_data.body) {
          usingCalendar = variant.step_data.body.includes(calendar_link_text);
          if (usingCalendar) {
            this.setState({ isUsingCalendarLinkMergeTag: usingCalendar });
            return;
          }
        }
        if ("subject" in variant.step_data && variant.step_data.subject) {
          usingCalendar =
            variant.step_data.subject.includes(calendar_link_text);
          if (usingCalendar) {
            this.setState({ isUsingCalendarLinkMergeTag: usingCalendar });
            return;
          }
        }
      });
    });
  }

  secondStepMessageContent = [
    {
      element: (
        <>
          <ul>
            <li>
              Starting campaign on Soft-Start is highly recommended. It helps
              you build up email reputation. Learn more{" "}
              <a
                href="https://help.smartreach.io/docs/soft-start"
                target="_blank"
              >
                here.
              </a>
            </li>
          </ul>
        </>
      ),
    },
  ];

  finalStepMessageContent (show_soft_start_setting: boolean){
    const message =

    [
    {
      element: (
        <>
          <ul className="mb-3 list-none">
            <li className="text-left">
              SmartReach runs pre-campaign checks before starting any new campaign.
            </li>
          </ul>
        </>
      ),
    }]

    if(show_soft_start_setting){
      const data =  message.concat(
        [
          {
            element: (
              <>
                <ul className="mb-3 list-none">
                  <li className="text-left">
                  It may take upto 40 minutes to send your first email from the campaign.
                  </li>
                </ul>
              </>
            ),
          }
        ]
      )
      return data
    } else{
      return message
    }
  }

  validateWarmupSettings() {
    const show_soft_start_setting  = this.state.show_soft_start_settings
    const { warmUpLengthInDays, warmUpStartingEmailCount } = this.state;
    let error = "";

    if(show_soft_start_setting){
      if (warmUpLengthInDays < 7) {
        error = "Soft-Start length should be more than or equal to 7 days";
      } else if (warmUpLengthInDays > 30) {
        error = "Soft-Start length should be less than or equal to 30 days";
      } else if (warmUpStartingEmailCount > 25) {
        error = `'Number of emails to be sent on first day of warm-up' should be less than or equal to 25`;
      }
    }

    return error;
  }

  getOptions(): ISRDropdownOption[] {
    return this.state.timezoneOptions.map((option) => {
      return {
        displayText: option.text,
        value: option.value,
      };
    });
  }

  handleSendingModeChange(mode: 'CoPilot' | 'AutoPilot') {
    this.setState({ sendingMode: mode });
  }

  isMagicContentCampaign() {
    return this.props.campaignStore?.getBasicInfo?.settings?.campaign_type === "magic_content";
  }

  isSecondLastStep() {
    const currentStep = this.state.startCampaignStep;
    // const isMagicContent = this.isMagicContentCampaign();
    const show_soft_start_setting = this.state.show_soft_start_settings;
    const hasOnlyManualSteps = this.state.hasOnlyManualSteps;

    // If only manual steps, second last step is ScheduleSettings
    if (hasOnlyManualSteps) {
      return currentStep === StartCampaignStep.ScheduleSettings;
    }

    // If soft start is shown, second last step is SoftStartSettings
    if (show_soft_start_setting) {
      return currentStep === StartCampaignStep.SoftStartSettings;
    }

    // Otherwise, second last step is ScheduleSettings
    return currentStep === StartCampaignStep.ScheduleSettings;
  }

  async handleSecondLastStepNext() {
    // Start the campaign on the second last step
    if (this.isSecondLastStep()) {
      this.setState({ isStartingCampaign: true });
      try {
        if (this.state.show_soft_start_settings && this.state.startCampaignWithOrWithoutWarmup === "with_warm_up") {
          await this.startWithWarmUp(
            this.state.warmUpLengthInDays,
            this.state.warmUpStartingEmailCount
          );
        } else {
          await this.startWithoutWamUp();
        }
        this.setState({ campaignStarted: true, isStartingCampaign: false });
        this.goToNextStep(); // Move to final step
      } catch (error) {
        this.setState({ isStartingCampaign: false });
        // Error handling is already in the start methods
      }
    } else {
      this.goToNextStep();
    }
  }

  getButtonText(){
    const isSecondLastStep = this.isSecondLastStep();
    const isCampaignPaused = this.props.isCampaignPaused;
    const scheduleStartLater = this.state.startCampaignNowOrLater === "later";

    if(isSecondLastStep){
      return scheduleStartLater ? "Schedule campaign" : isCampaignPaused ? "Resume Campaign" : "Start Campaign";
    }
    return 'Next';
  }

  render() {
    const canEdit = this.props.canEditSetting;
    const currentStep = this.state.startCampaignStep || StartCampaignStep.SendingModeSelection;
    const scheduleStartLater = this.state.startCampaignNowOrLater === "later";
    const show_soft_start_setting = this.state.show_soft_start_settings
    const accountInfo = this.props.logInStore?.accountInfo
    const isMagicContent = this.isMagicContentCampaign();

    // console.log("currentStep :: ", currentStep, " isMagicContent :: ", isMagicContent, " show_soft_start_setting :: ", show_soft_start_setting);

    const isCampaignPaused = this.props.isCampaignPaused;

    const title = getModalTitle({
      currentStep: currentStep,
      isCampaignPaused: isCampaignPaused,
      campaignStarted: this.state.campaignStarted,
    });

    const camapignSubheaderStatement = this.state.campaignName ? `${this.state.campaignName} has now been ${scheduleStartLater ? "scheduled" : isCampaignPaused ? "resumed" : "started"}` : "";

    // let startCampaignButtonText = isCampaignPaused
    //   ? "Resume Campaign"
    //   : "Start Campaign";
    // if (scheduleStartLater) {
    //   startCampaignButtonText = "Schedule campaign";
    // }

    console.log("isUsingCalendarLinkMergeTag");
    console.log(this.state.isUsingCalendarLinkMergeTag);
    const isCalendarEnabled = !!accountInfo?.org.org_metadata.enable_calendar  && !!accountInfo?.calendar_account_data;
    const calendarUserId =
      isCalendarEnabled ? accountInfo?.calendar_account_data.calendar_user_id : undefined;
    const campaignSettings =
      this.props.campaignStore?.currentCampaign.basicInfo.settings;
    // const isEventTypeSelected =
    //   this.props.logInStore?.accountInfo.calendar_account_data
    //     .default_cal_event_type_id ||
    //   (campaignSettings &&
    //     campaignSettings.calendar_settings_data &&
    //     campaignSettings.calendar_settings_data.default_cal_event_type_id
    //     ? true
    //     : false);

    const team = this.props.logInStore?.accountInfo.teams.find(t => t.team_id == this.props.logInStore?.getCurrentTeamId)
    const isEventTypeSelected =
    !!team?.selected_calendar_data?.calendar_event_type_id ||
      (campaignSettings &&
        campaignSettings.selected_calendar_data &&
        campaignSettings.selected_calendar_data.calendar_event_type_id
        ? true
        : false);

    console.log(isCalendarEnabled);
    console.log(calendarUserId);
    console.log(isEventTypeSelected);
    const showModal =
      this.state.isUsingCalendarLinkMergeTag &&
      (!isCalendarEnabled || !calendarUserId || !isEventTypeSelected);

    // Determine final step number based on campaign type and settings
    const getFinalStepNumber = () => {
      return StartCampaignStep.StartCampaign;
      // if (show_soft_start_setting) {
      //   return isMagicContent ? StartCampaignStep.StartCampaign : StartCampaignStep.SoftStartSettings;
      // } else {
      //   return isMagicContent ? StartCampaignStep.StartCampaign : StartCampaignStep.SoftStartSettings; // 0 and 1
      // }
    };

  const buttonText = this.getButtonText()


    const finalStepNumber = getFinalStepNumber();

    return (
      <div>
        {showModal && (
          <SrModal
            title={"Missing Calendar Link Configuration"}
            content={
              <>
                You have not configured Calendar for current campaign.
                <div>
                  <SRButtonOutline
                    text="Go to Calendar Settings"
                    onClick={() =>
                      (window.location.href = `/dashboard/campaigns/${this.props.campaignId}/settings/calendar_settings?tid=${this.props.campaignStore?.getBasicInfo.team_id}`)
                    }
                    isPrimary
                    icon="sr_icon_chevron_right"
                    iconPosition="left"
                    iconClassName="text-sr-default-blue"
                    className="bg-white my-3"
                  />
                </div>
              </>
            }
            onClose={() => this.props.onClose}
            showCloseButton={false}
          />
        )}
        {
          !showModal && (
            <SrModal
              size={(currentStep === StartCampaignStep.SoftStartSettings && this.state.startCampaignWithOrWithoutWarmup === "with_warm_up")  ? "medium" : "small"}
              onClose={this.props?.onClose as () => {}}
              title={title}
              content={
                <div className='sr-p-basic text-sr-gray-100'>

                  {currentStep === StartCampaignStep.SendingModeSelection && isMagicContent && (
                     <div>
                       <SendingModeSelection
                         value={this.state.sendingMode}
                         onChange={this.handleSendingModeChange}
                         disabled={false}
                         is_start_flow={true}
                       />
                     </div>
                   )}

                  {(currentStep === StartCampaignStep.ScheduleSettings ) && (
                    <Formik initialValues={{}} onSubmit={() => { }}>
                      {() => (
                        <Form>
                          <div
                            className="flex my-3 ml-1"
                            id="my-radio-group"
                          >
                          </div>
                          <div
                            className="font-sourcesanspro"
                            role="group"
                            aria-labelledby="my-radio-group"
                          >
                            <label className="flex items-center mb-4 cursor-pointer">
                              <Field
                                className="mr-2"
                                type="radio"
                                name="campaignStartSchedule"
                                value="now"
                                checked={
                                  this.state.startCampaignNowOrLater === "now"
                                }
                                onChange={() => {
                                  this.handleCampaignStartScheduleChange(
                                    "now",
                                    { value: "now" }
                                  );
                                }}
                              />
                              <span className='ml-1'>{ isCampaignPaused ? "Resume" : "Start" } campaign now</span>
                            </label>
                            <label className="flex items-center mb-4 cursor-pointer">
                              <Field
                                className="mr-2"
                                type="radio"
                                name="campaignStartSchedule"
                                value="later"
                                checked={scheduleStartLater}
                                onChange={() => {
                                  this.handleCampaignStartScheduleChange(
                                    "later",
                                    { value: "later" }
                                  );
                                }}
                              />
                              <span className='ml-1'>Schedule campaign to { isCampaignPaused ? "resume" : "start" } later</span>
                            </label>

                            {scheduleStartLater && (
                              <div className="flex justify-between mt-[35px]">
                                <div className="w-[226px]">
                                  <label className="sr-p-basic text-sr-gray-90 mb-1 block">
                                    { isCampaignPaused ? "Resume" : "Start" } date
                                  </label>
                                  <div className="flex-1">
                                    <DatePicker
                                      selected={this.state.campaignStartDate}
                                      onChange={
                                        this.handleCampaignStartDateChange
                                      }
                                      dateFormat={"MMM dd, yyyy"}
                                      minDate={
                                        new Date(Date.now() + 3600 * 1000 * 24)
                                      }
                                      className="block !w-full mb-2.5 rounded-md sr-p-basic border border-gray-300 bg-white py-2 pl-3 pr-10 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                      popperPlacement="top"
                                    />
                                  </div>
                                </div>
                                <div className="w-[226px]">
                                  <SRSimpleSelectionDropdown
                                    label='Timezone'
                                    largerFontSize
                                    placeholder="e.g. Asia/Kolkata"
                                    width="fluid"
                                    options={this.getOptions()}
                                    selectedValue={
                                      this.state.selectedTimezone as string
                                    }
                                    loading={this.state.isFetchingTimezones}
                                    handleChange={(option) =>
                                      this.handleTimezoneChange(option, {
                                        value: option.value,
                                      })
                                    }
                                    dropdownButtonClassName='!h-[37px]'
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                        </Form>
                      )}
                    </Formik>
                  )}

                  {( currentStep === StartCampaignStep.SoftStartSettings ) && (
                    <div>
                      <div style={{ marginTop: "15px", marginBottom: "25px" }}>
                        <SRMessageBox width='fluid' type="info" content={this.secondStepMessageContent} alignTextLeft={true} />
                      </div>
                      <Formik initialValues={{}} onSubmit={() => { }}>
                        {() => (
                          <Form>
                            <div
                              className="font-sourcesanspro"
                              role="group"
                              aria-labelledby="my-radio-group"
                            >
                              <label className="flex items-center mb-4 cursor-pointer">
                                <Field
                                  className="mr-2"
                                  type="radio"
                                  name="campaignWarmUp"
                                  value="with_warm_up"
                                  checked={
                                    this.state
                                      .startCampaignWithOrWithoutWarmup ===
                                    "with_warm_up"
                                  }
                                  onChange={() => {
                                    this.handleWarmUpSettingChange(
                                      "with_warm_up",
                                      { value: "with_warm_up" }
                                    );
                                  }}
                                />
                                <span className='ml-1'>{ isCampaignPaused ? "Resume" : "Start" } campaign on Soft-Start</span>
                              </label>
                              <label className="flex items-center mb-4 cursor-pointer">
                                <Field
                                  className="mr-2"
                                  type="radio"
                                  name="campaignWarmUp"
                                  value="without_warm_up"
                                  checked={
                                    this.state
                                      .startCampaignWithOrWithoutWarmup ===
                                    "without_warm_up"
                                  }
                                  onChange={() => {
                                    this.handleWarmUpSettingChange(
                                      "without_warm_up",
                                      { value: "without_warm_up" }
                                    );
                                  }}
                                />
                                <span className='ml-1'>{ isCampaignPaused ? "Resume" : "Start" } campaign without Soft-Start</span>
                              </label>

                              {this.state.startCampaignWithOrWithoutWarmup ===
                                "with_warm_up" && (
                                  <div className="flex justify-between mt-4">
                                    <div className="w-[400px]">
                                      <label className="sr-p-basic text-sr-gray-90 mb-1 block my-4">
                                        {
                                          "Soft-Start length in Days (upto 30 days):"
                                        }
                                      </label>
                                      <Field
                                        className="rounded-md w-full"
                                        type="number"
                                        value={this.state.warmUpLengthInDays}
                                        onChange={(e: any) =>
                                          this.handleWarmupLengthInDaysChange(
                                            "",
                                            {
                                              value: e.target.value,
                                            }
                                          )
                                        }
                                      />
                                    </div>
                                    <div className="w-[400px]">
                                      <label className="sr-p-basic text-sr-gray-90 mb-1 block my-4">
                                        {
                                          "Number of emails to be sent on first day of Soft-Start:"
                                        }
                                      </label>
                                      <Field
                                        className="rounded-md w-full"
                                        type="number"
                                        value={
                                          this.state.warmUpStartingEmailCount
                                        }
                                        onChange={(e: any) =>
                                          this.handleWarmUpStartingEmailCountChange(
                                            "",
                                            {
                                              value: e.target.value,
                                            }
                                          )
                                        }
                                      />
                                    </div>
                                  </div>
                                )}
                            </div>
                          </Form>
                        )}
                      </Formik>
                      {this.state.startCampaignWithOrWithoutWarmup ===
                        "with_warm_up" &&
                        !!this.validateWarmupSettings() && (
                          <div className="ui error message">
                            {this.validateWarmupSettings()}
                          </div>
                        )}
                    </div>
                  )}

                  {currentStep === finalStepNumber && !this.state.campaignStarted && (
                    <SRMessageBox
                      content={this.finalStepMessageContent(show_soft_start_setting)}
                      width='fluid'
                      type='info'
                      alignTextLeft={true}
                      className='mt-3 !p-0 !bg-white'
                    />
                  )}

                  {currentStep === finalStepNumber && (
                    <div className="text-center p-5 my-6">
                      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <div className="text-xl font-semibold text-gray-900 mb-3">Campaign { scheduleStartLater ? "scheduled" : isCampaignPaused ? "resumed" : "started" }</div>
                      <div className='sr-p-normal text-sr-gray-100 mb-4'>{camapignSubheaderStatement}</div>
                      <div className="bg-blue-50 rounded-lg mt-6 mb-4 px-2">
                        <div className='p-3 text-sm text-blue-700 mb-2'>
                          { this.finalStepMessageContent(this.state.show_soft_start_settings)
                               .map((item, idx) => <div key={idx}>{item.element}</div>) }
                       </div>
                      </div>

                      {this.state.hasAnyManualSteps && (
                        <>
                        <div className='flex justify-between bg-sr-gray-10 rounded-lg p-3 mb-4'>

                         <div> View your tasks</div>
                         <div
                          className='flex items-center cursor-pointer'
                          onClick={() => {
                              this.props.onClose?.();
                              this.props.history.push('/dashboard/tasks');
                            }}>
                              <div className='flex items-center gap-2'>
                          <span className='text-sr-primary-90 sr-h6'>Go to tasks</span>
                          <SRIconArrowRight className='!w-4 !h-4 text-sr-primary-90' />
                          </div>
                         </div>

                        </div>
                        <div>
                        <div className='flex justify-between bg-sr-gray-10 rounded-lg p-3'>

                          <div> Learn about task management</div>
                          <div
                           className='flex items-center cursor-pointer'>
                           <a href="https://help.smartreach.io/docs/how-to-manage-multichannel-tasks" target="_blank" rel="noopener noreferrer">
                           <div className='flex items-center gap-2'>
                           <span className='text-sr-primary-90 sr-h6'>Help Docs</span>
                           <SrIconExternalIcon className='!w-4 !h-4 text-sr-primary-90' />
                           </div>
                           </a>
                          </div>

                          </div>
                        </div>
                        </>
                      )}
                    </div>
                  )}

                  <div className={`flex ${currentStep === finalStepNumber ? '' : 'justify-end mt-[40px]'}`}>
                    {shouldShowPreviousButton({
                      currentStep: currentStep,
                      isMagicContent: isMagicContent
                    }) && (
                      <SRButtonOutline
                      isPrimary
                        onClick={this.goToPreviousStep.bind(this)}
                        className='mr-4 w-[120px]'
                        text='Previous'
                      />
                    )}

                    {currentStep === finalStepNumber ? (
                      <div className="px-5 pb-5 !w-full">
                      <SRButtonFilled
                        text='Close'
                        onClick={() => this.onClose()}
                        width="fluid"
                        className='!py-2 !text-base !font-sourcesanspro !bg-sr-gray-20 !text-sr-gray-100'
                        />
                        </div>
                    ): (
                      <SRButtonFilled
                        onClick={this.isSecondLastStep() ? this.handleSecondLastStepNext.bind(this) : this.goToNextStep.bind(this)}
                        isPrimary={true}
                        loading={this.isSecondLastStep() ? this.state.isStartingCampaign : false}
                        disable={this.isNextDisabled() || (this.isSecondLastStep() && (
                          this.isStartDisabled() ||
                          !canEdit ||
                          !!this.validateWarmupSettings()
                        ))}
                        text={buttonText}
                        className='w-[120px] py-1 text-base'
                      />
                    )}
                  </div>
                </div>
              }
            />
          )
        }
      </div>
    );
  }
}
export const StartCampaignModal = withRouter(inject('campaignStore', 'logInStore', 'alertStore')(observer(StartCampaignModalComponent)));

