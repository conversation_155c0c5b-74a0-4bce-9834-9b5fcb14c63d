# .github/workflows/deploy-and-release.yml

name: Create Release, Build, and Deploy Frontend to Production

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'The version for the release (e.g., v1.2.3)'
        required: true
        type: string

permissions:
  contents: write
  id-token: write

jobs:
  create-release-build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository code
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # Step 2: Set up Node.js
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          # Cache is configured for the root package-lock.json, but npm is smart enough
          # to use the cache for packages needed by post-login/package-lock.json
          cache-dependency-path: 'post-login/package-lock.json'

      # Step 3: Authenticate the runner environment to Google Cloud
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      # Step 4: Set up the gcloud CLI tool
      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      # ====================================================================
      # NEW STEP: Add this step to verify the active account
      # ====================================================================
      - name: Verify Google Cloud Authentication
        run: |
          echo "--- Verifying active gcloud account ---"
          gcloud auth list
          echo "-------------------------------------"
          echo "Active Account (direct check):"
          gcloud config list account --format "value(core.account)"
          echo "-------------------------------------"

      # ====================================================================
      # Build the application on the GitHub Runner
      # ====================================================================

      # Step 6: Configure NPM to authenticate with Google Artifact Registry
      # MODIFIED: Points to the single Virtual Repository which contains all upstream npm repos.
      - name: Configure NPM for Artifact Registry
        working-directory: ./post-login
        run: |
          echo "--- Creating fresh .npmrc for CI for @sr scope ---"
          gcloud artifacts print-settings npm \
            --project=smrtcloud \
            --repository=sr-virtual-frontend \
            --location=us-central1 \
            --scope=@sr > .npmrc
          echo "--- Appending configuration for @srio scope ---"
          gcloud artifacts print-settings npm \
            --project=smrtcloud \
            --repository=sr-virtual-frontend \
            --location=us-central1 \
            --scope=@srio >> .npmrc
          echo "Successfully configured .npmrc for all scopes."
          echo "--- .npmrc content ---"
          cat .npmrc

      # ====================================================================
      # EMERGENCY DEBUGGING STEP: This replaces the normal "npm ci".
      # ====================================================================
      - name: 'DEBUG: Install Dependencies'
        working-directory: ./post-login
        run: |
          #echo "--- STARTING DEEP DEBUG ---"
          #echo "--- 1. Verifying final NPM configuration ---"
          #npm config list
          #echo "-------------------------------------------"
          #echo "--- 2. Attempting to view a specific known private package ---"
          #npm view @sr/design-component --verbose
          #echo "-------------------------------------------"
          #echo "--- 3. Running npm ci with maximum verbosity ---"
          #npm ci --legacy-peer-deps --verbose
          #echo "-------------------------------------------"
          #echo "--- END DEEP DEBUG ---"
          echo "--- SKipping DEBUG ---"


      # Step 7: Install Node.js dependencies
      - name: Install Dependencies
        working-directory: ./post-login
        run: npm ci --legacy-peer-deps

      # Step 8: Build the application
      - name: Run Build
        working-directory: ./post-login
        run: npm run build-without-test

      # Step 8: Create the release artifact (tarball)
      - name: Create Release Tarball
        working-directory: ./post-login
        run: |
          echo "--- Creating release tarball for version ${{ github.event.inputs.version }} ---"
          tar -czf ${{ github.event.inputs.version }}.tar.gz sr

      # Step 9: Upload artifact to Google Cloud Storage
      - name: Upload Release Tarball to GCS
        working-directory: ./post-login
        run: |
          echo "--- Uploading ${{ github.event.inputs.version }}.tar.gz to GCS ---"
          gsutil -m rsync -r  sr gs://cdn-sr/sr
          gsutil cp ${{ github.event.inputs.version }}.tar.gz gs://sr-fe-releases/releases/



      # Step 10: Create GitHub Release (This creates the tag)
      # This happens ONLY after the artifact is successfully built and uploaded.
      - name: Create GitHub Release
        id: create_release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.event.inputs.version }}
          name: "${{ github.event.inputs.version }}"
          generate_release_notes: true
          # Optional: You can also upload the tarball to the release page itself
          # files: ./post-login/${{ github.event.inputs.version }}.tar.gz


      # Step 11: Update Server to New Git Tag (This now finds the tag)
      - name: Update Server to New Git Tag
        env:
          FRONTEND_HOST: sr-frontend-4
          ZONE:          us-central1-a
          DEPLOY_DIR:    /home/<USER>/sr_postlogin_v3/post-login
          VERSION_TAG:   ${{ github.event.inputs.version }}
        run: |
          echo "Waiting 5 seconds for tag to propagate..."
          sleep 10

          gcloud compute ssh --zone $ZONE ubuntu@$FRONTEND_HOST \
            --tunnel-through-iap \
            --quiet \
            --ssh-flag="-o IdentitiesOnly=yes" \
            --command="
              set -e
              echo '---  Waiting 20 seconds to gsutils cp to google cloud storage for file to appear ---'
              echo '     we have had an error that file was not available. but when we checked gcs it was visible.'
              sleep 40
              echo '--- Connecting to server: $FRONTEND_HOST ---'
              cd $DEPLOY_DIR

              echo '--- Setting up SSH agent for git authentication ---'
              eval \"\$(ssh-agent -s)\"
              ssh-add ~/.ssh/sr-github-deploy-key-prod-1

              echo '--- Fetching all new tags from the remote repository ---'
              git fetch origin --tags

              echo '--- Checking out the specific release tag: $VERSION_TAG ---'
              git checkout $VERSION_TAG
              gsutil cp gs://sr-fe-releases/releases/${{ github.event.inputs.version }}.tar.gz .

              # CORRECTED: Escaped all '$DIRECTORY' variables to prevent local shell expansion
              DIRECTORY=sr.prev2
              if [ -d \"\$DIRECTORY\" ]; then
                  echo \"Directory '\$DIRECTORY' exists. Removing it...\"
                  rm -rf \"\$DIRECTORY\"
                  echo \"Directory '\$DIRECTORY' removed.\"
              else
                  echo \"Directory '\$DIRECTORY' does not exist.\"
              fi

              DIRECTORY=sr.prev
              if [ -d \"\$DIRECTORY\" ]; then
                  echo \"Directory '\$DIRECTORY' exists. Renaming it to sr.prev2\"
                  mv \"\$DIRECTORY\" sr.prev2
                  echo \"Directory renamed.\"
              else
                  echo \"Directory '\$DIRECTORY' does not exist.\"
              fi

              DIRECTORY=sr
              if [ -d \"\$DIRECTORY\" ]; then
                  echo \"Directory '\$DIRECTORY' exists. Renaming it to sr.prev\"
                  mv \"\$DIRECTORY\" sr.prev
                  echo \"Directory renamed.\"
              else
                  echo \"Directory '\$DIRECTORY' does not exist.\"
              fi

              # extract the file which will open into sr/public/public by convention
              tar -xvf ${{ github.event.inputs.version }}.tar.gz

              echo '--- Server code successfully updated to version $VERSION_TAG ---. Now removing the downloaded tar.'
              rm ${{ github.event.inputs.version }}.tar.gz


            "

      # Step 12: Health check
      - name: Verify deployment
        run: |
          echo "Waiting for 10 seconds before health check..."
          sleep 10
          echo "Performing health check on https://app.smartreach.io"
          curl -s -f "https://app.smartreach.io" || exit 1
          echo "Health check passed!"

